import React, { useState, useContext } from "react";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { ErrorMessage, Field, Formik } from "formik";
import * as Yup from "yup";
import { useNavigate, Link } from "react-router-dom";
import {
  fetchFromStorage,
  saveToStorage,
} from "../../../../helpers/context/storage";
import login from "../../../../assets/images/Login/sign-in.svg";
import X from "../../../../assets/images/Login/X.svg";
import facebook_Bottom from "../../../../assets/images/Login/Facebook_Bottom.svg";
import facebookk from "../../../../assets/images/Login/Facebookk.svg";
import Instagram_Top from "../../../../assets/images/Login/Instagram_Top.svg";
import Instagram from "../../../../assets/images/Login/Instagram.svg";
import Pinterest_Right from "../../../../assets/images/Login/Pinterest_Right.svg";
import Pinterest from "../../../../assets/images/Login/Pinterest.svg";
import Tiktok from "../../../../assets/images/Login/Tiktok.svg";
import X_Right from "../../../../assets/images/Login/X_Right.svg";
import Youtube from "../../../../assets/images/Login/Youtube.svg";
import Email from "../../../../assets/images/Login/Email.svg";
import Lock from "../../../../assets/images/Login/Lock.svg";
import ShowPass from "../../../../assets/images/ForgotPass/SeePass.svg";
import HidePass from "../../../../assets/images/ForgotPass/HidePass.svg";
import { CustomTextField } from "../../custom/CustomTextField";
import { URL } from "../../../../helpers/constant/Url";
import { setApiMessage } from "../../../../helpers/context/toaster";
import siteConstant from "../../../../helpers/constant/siteConstant";
import { IntlContext } from "../../../../App";
import LoadingSpinner from "../../../../helpers/UI/LoadingSpinner";
import apiInstance from "../../../../helpers/Axios/axiosINstance";
import { useMultiAccount } from "../../../../helpers/context/MultiAccountContext";

const AddAccountSignIn = () => {
  const [open, setOpen] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const navigate = useNavigate();
  const { addStoredAccount } = useMultiAccount();

  // Validation schema
  const validationSchema = Yup.object().shape({
    email: Yup.string()
      .email(
        localesData?.USER_WEB?.MESSAGES?.INVALID_EMAIL_ADDRESS ||
          "Invalid email address"
      )
      .required(
        localesData?.USER_WEB?.MESSAGES?.EMAIL_REQUIRED || "Email is required"
      ),
    password: Yup.string().required(
      localesData?.USER_WEB?.MESSAGES?.PASSWORD_REQUIRED ||
        "Password is required"
    ),
  });

  // Handle form submission
  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    setLoading(true);
    setError("");

    try {
      const form = new FormData();
      form.append("creds", values.email);
      form.append("password", values.password);

      const { status, data } = await apiInstance.post(URL.LOGIN_SWITCH, form);

      if (data?.status && data?.token) {
        // Create account object for storage
        const brandId =
          data.brand_id ||
          data.brandId ||
          parseInt(localStorage.getItem("BrandId"), 10) ||
          1;

        console.log(
          "AddAccount: Setting account with brandId:",
          brandId,
          "from sources:",
          {
            apiResponseBrandId: data.brand_id,
            apiResponseBrandIdAlt: data.brandId,
            storedBrandId: localStorage.getItem("BrandId"),
          }
        );

        const accountData = {
          userId: data.user_id || data.id,
          name: data.name,
          username: data.username,
          profileImage: data.profile_image || "",
          token: data.token,
          email: values.email,
          brandId: brandId,
          isMainAccount: false,
        };

        // Add to stored accounts
        addStoredAccount(accountData);

        // Show success message and redirect
        setError(
          "Account added successfully! You can now switch to this account from the dropdown."
        );
        setTimeout(() => {
          navigate("/dashboard");
        }, 2000);
      } else {
        setError(data?.message || "Login failed");
      }
    } catch (error) {
      console.error("Login error:", error);
      setError(
        error?.message || "Login failed. Please check your credentials."
      );
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  // Background social media icons component
  const BackgroundIcons = () => (
    <div className="pointer-events-none absolute inset-0 z-0">
      {/* Desktop icons */}
      <div className="hidden md:block">
        <img
          src={X}
          alt="X Logo"
          className="absolute top-0 left-40 max-w-full h-auto"
        />
        <img
          src={facebookk}
          alt="Facebook Logo"
          className="absolute top-[25%] left-0 max-w-full h-auto"
        />
        <img
          src={Instagram}
          alt="Instagram Logo"
          className="absolute bottom-16 left-[15%] max-w-full h-auto"
        />
        <img
          src={facebook_Bottom}
          alt="Facebook Bottom Logo"
          className="absolute bottom-0 left-[50%] max-w-full h-auto"
        />
        <img
          src={X_Right}
          alt="X Right Logo"
          className="absolute bottom-[10%] right-[30%] max-w-full h-auto"
        />
        <img
          src={Pinterest_Right}
          alt="Pinterest Right Logo"
          className="absolute bottom-[5%] right-[10%] max-w-full h-auto"
        />
        <img
          src={Youtube}
          alt="Youtube Logo"
          className="absolute bottom-[50%] right-[2%] max-w-full h-auto"
        />
        <img
          src={Instagram_Top}
          alt="Instagram Top Logo"
          className="absolute top-[5%] right-[5%] max-w-full h-auto"
        />
        <img
          src={X_Right}
          alt="X Top Logo"
          className="absolute top-[10%] right-[30%] max-w-full h-auto"
        />
        <img
          src={Tiktok}
          alt="Tiktok Logo"
          className="absolute top-[4%] right-[50%] max-w-full h-auto"
        />
        <img
          src={Pinterest}
          alt="Pinterest Logo"
          className="absolute top-[25%] right-[50%] max-w-full h-auto"
        />
      </div>

      {/* Mobile icons - simplified */}
      <div className="md:hidden">
        <img src={X} alt="X Logo" className="absolute top-2 left-2 w-10 h-10" />
        <img
          src={Instagram}
          alt="Instagram Logo"
          className="absolute bottom-2 right-2 w-10 h-10"
        />
      </div>
    </div>
  );

  // Input field styles
  const getInputStyles = (touched, error) => ({
    "& .MuiOutlinedInput-root": {
      paddingLeft: "45px",
      paddingRight: "45px",
      "& fieldset": {
        borderRadius: "20px",
        borderColor: touched && error ? "#f44336" : undefined,
      },
      "&:hover fieldset": {
        borderColor: touched && error ? "#f44336" : undefined,
      },
      "&.Mui-focused fieldset": {
        borderColor: touched && error ? "#f44336" : "#614036",
      },
    },
    "& .MuiOutlinedInput-input": {
      paddingLeft: "10px !important",
      paddingRight: "10px !important",
      color: "#000000 !important",
      fontWeight: "normal !important",
      "&:-webkit-autofill": {
        WebkitTextFillColor: "#000000 !important",
        WebkitBoxShadow: "0 0 0 1000px white inset !important",
        fontWeight: "normal !important",
      },
    },
    "& .MuiInputLabel-root": {
      left: "35px",
      transform: "translate(14px, 16px) scale(1)",
      color: "#000000 !important",
      "&.MuiInputLabel-shrink": {
        transform: "translate(5px, -9px) scale(0.75)",
      },
      "&.Mui-focused": {
        color: "#000000 !important",
      },
    },
  });

  return (
    <div className="h-screen overflow-y-auto">
      <div className="relative min-h-screen bg-white flex flex-col items-center px-4 font-Ubuntu max-w-[100%] mx-auto sm:mb-0 lg:mb-10 2xl:mb-0">
        {/* Background Icons */}
        <BackgroundIcons />

        {/* Main Content */}
        <div className="relative z-10 w-full flex flex-col justify-center items-center md:flex-row md:-ml-7 mt-6 sm:mt-12  2xl:mt-28">
          {/* Login Form */}
          <div className="w-full md:w-[50%] max-w-md mx-auto md:ml-[133px] bg-white bg-opacity-90 rounded-xl p-4 md:p-0 shadow md:shadow-none">
            <h2 className="text-3xl text-[#614036] mb-2 z-50">
              <span className="font-bold">Sign</span> In
            </h2>

            <p className="text-[#AA8882] mb-8 z-50">
              Access your account and continue exploring
            </p>

            {/* Display error message */}
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            <Formik
              initialValues={{ email: "", password: "" }}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
              validateOnBlur={false}
              validateOnChange={false}
            >
              {({
                errors,
                handleBlur,
                handleChange,
                handleSubmit,
                touched,
                values,
                isSubmitting,
              }) => (
                <form onSubmit={handleSubmit}>
                  {/* Email Input Field */}
                  <div className="mb-6 relative">
                    <span className="absolute top-[34%] -translate-y-1/2 left-4 flex items-center pointer-events-none z-10">
                      <img src={Email} alt="Email" className="w-5 h-5" />
                    </span>
                    <Field
                      as={CustomTextField}
                      variant="outlined"
                      borderRadius="12px"
                      label="Email"
                      fullWidth
                      type="text"
                      id="email"
                      name="email"
                      value={values.email}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.email && errors.email}
                      sx={getInputStyles(touched.email, errors.email)}
                    />
                    <div
                      className={`h-5 mt-1 ${
                        touched.email && errors.email ? "visible" : "invisible"
                      }`}
                    >
                      <p className="text-red-500 text-sm ml-1">
                        {errors.email}
                      </p>
                    </div>
                  </div>

                  {/* Password Input Field */}
                  <div className="mb-6 relative">
                    <span className="absolute top-[34%] -translate-y-1/2 left-4 flex items-center pointer-events-none z-10">
                      <img src={Lock} alt="Lock" className="w-5 h-5" />
                    </span>
                    <Field
                      as={CustomTextField}
                      variant="outlined"
                      borderRadius="12px"
                      label="Password"
                      fullWidth
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      value={values.password}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.password && errors.password}
                      sx={getInputStyles(touched.password, errors.password)}
                    />
                    <button
                      type="button"
                      className="absolute top-[34%] -translate-y-1/2 right-4 flex items-center z-50"
                      onClick={() => setShowPassword(!showPassword)}
                      aria-label={
                        showPassword ? "Hide password" : "Show password"
                      }
                    >
                      <img
                        src={showPassword ? ShowPass : HidePass}
                        alt={showPassword ? "Hide password" : "Show password"}
                        className="h-[20px] w-[20px]"
                      />
                    </button>
                    <div
                      className={`h-5 mt-1 ${
                        touched.password && errors.password
                          ? "visible"
                          : "invisible"
                      }`}
                    >
                      <p className="text-red-500 text-sm ml-1">
                        {errors.password}
                      </p>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-center items-center mb-10">
                    <button
                      type="submit"
                      className={`w-[50%] py-3 rounded-[12px] font-medium transition duration-300 ${
                        loading || isSubmitting
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-[#614036] hover:bg-[#7a5046] text-white focus:outline-none focus:ring-2 focus:ring-[#614036] focus:ring-opacity-50"
                      }`}
                      disabled={loading || isSubmitting}
                    >
                      {loading || isSubmitting ? (
                        <LoadingSpinner
                          text="Loading..."
                          spinnerSize="h-4 w-4"
                          textColor="text-white"
                        />
                      ) : (
                        <span>
                          {localesData?.USER_WEB?.SIGN_IN || "Sign In"}
                        </span>
                      )}
                    </button>
                  </div>
                </form>
              )}
            </Formik>
          </div>

          {/* Illustration */}
          <div className="w-[50%]">
            <div className="flex justify-center items-center h-full">
              <img
                src={login}
                alt="Login illustration"
                className="max-w-full h-auto hidden md:block"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddAccountSignIn;
