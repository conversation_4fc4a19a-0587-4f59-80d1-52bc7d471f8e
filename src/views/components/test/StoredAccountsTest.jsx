import React, { useState, useEffect } from 'react';
import { useMultiAccount } from '../../../helpers/context/MultiAccountContext';
import { fetchFromStorage } from '../../../helpers/context/storage';
import siteConstant from '../../../helpers/constant/siteConstant';

/**
 * Test component to verify that storedAccounts are properly updated when profile is edited
 * This component shows the current state of storedAccounts and USERDATA in localStorage
 */
const StoredAccountsTest = () => {
  const { storedAccounts, selectedAccount } = useMultiAccount();
  const [userDataFromStorage, setUserDataFromStorage] = useState(null);
  const [storedAccountsFromStorage, setStoredAccountsFromStorage] = useState([]);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Function to refresh data from localStorage
  const refreshData = () => {
    const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
    const storedAccountsData = localStorage.getItem("storedAccounts");
    
    setUserDataFromStorage(userData);
    setStoredAccountsFromStorage(
      storedAccountsData ? JSON.parse(storedAccountsData) : []
    );
    setLastUpdate(new Date().toLocaleString());
  };

  // Refresh data on mount and set up interval
  useEffect(() => {
    refreshData();
    
    // Refresh every 2 seconds to catch updates
    const interval = setInterval(refreshData, 2000);
    
    return () => clearInterval(interval);
  }, []);

  // Also refresh when storedAccounts from context changes
  useEffect(() => {
    refreshData();
  }, [storedAccounts]);

  const currentUserId = userDataFromStorage?.userId || userDataFromStorage?.user_id || userDataFromStorage?.id;
  const currentUserInStoredAccounts = storedAccountsFromStorage.find(
    account => account.userId === currentUserId
  );

  return (
    <div className="p-6 max-w-6xl mx-auto bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        Stored Accounts Sync Test
      </h2>
      
      <div className="mb-4 p-3 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-700">
          <strong>Last Updated:</strong> {lastUpdate}
        </p>
        <p className="text-sm text-blue-700">
          <strong>Current User ID:</strong> {currentUserId || 'Not found'}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* USERDATA from localStorage */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">
            USERDATA (localStorage)
          </h3>
          {userDataFromStorage ? (
            <div className="space-y-2 text-sm">
              <div><strong>Name:</strong> {userDataFromStorage.name || 'N/A'}</div>
              <div><strong>Username:</strong> {userDataFromStorage.username || 'N/A'}</div>
              <div><strong>Profile Image:</strong> {userDataFromStorage.profile_image || 'N/A'}</div>
              <div><strong>User ID:</strong> {currentUserId || 'N/A'}</div>
              <div><strong>Email:</strong> {userDataFromStorage.email || 'N/A'}</div>
            </div>
          ) : (
            <p className="text-gray-500">No user data found</p>
          )}
        </div>

        {/* Current User in Stored Accounts */}
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">
            Current User in Stored Accounts
          </h3>
          {currentUserInStoredAccounts ? (
            <div className="space-y-2 text-sm">
              <div><strong>Name:</strong> {currentUserInStoredAccounts.name || 'N/A'}</div>
              <div><strong>Username:</strong> {currentUserInStoredAccounts.username || 'N/A'}</div>
              <div><strong>Profile Image:</strong> {currentUserInStoredAccounts.profileImage || 'N/A'}</div>
              <div><strong>User ID:</strong> {currentUserInStoredAccounts.userId || 'N/A'}</div>
              <div><strong>Is Main Account:</strong> {currentUserInStoredAccounts.isMainAccount ? 'Yes' : 'No'}</div>
            </div>
          ) : (
            <p className="text-red-500">Current user not found in stored accounts!</p>
          )}
        </div>
      </div>

      {/* All Stored Accounts */}
      <div className="mt-6 bg-yellow-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-gray-700">
          All Stored Accounts ({storedAccountsFromStorage.length})
        </h3>
        {storedAccountsFromStorage.length > 0 ? (
          <div className="space-y-3">
            {storedAccountsFromStorage.map((account, index) => (
              <div key={account.userId || index} className="bg-white p-3 rounded border">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                  <div><strong>Name:</strong> {account.name || 'N/A'}</div>
                  <div><strong>Username:</strong> {account.username || 'N/A'}</div>
                  <div><strong>User ID:</strong> {account.userId || 'N/A'}</div>
                  <div><strong>Main:</strong> {account.isMainAccount ? 'Yes' : 'No'}</div>
                </div>
                {account.profileImage && (
                  <div className="mt-2 text-xs text-gray-600">
                    <strong>Profile Image:</strong> {account.profileImage}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No stored accounts found</p>
        )}
      </div>

      {/* Sync Status */}
      <div className="mt-6 p-4 rounded-lg border-2 border-dashed border-gray-300">
        <h3 className="text-lg font-semibold mb-3 text-gray-700">Sync Status</h3>
        {userDataFromStorage && currentUserInStoredAccounts ? (
          <div className="space-y-2 text-sm">
            <div className={`p-2 rounded ${
              userDataFromStorage.name === currentUserInStoredAccounts.name 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              <strong>Name Sync:</strong> {
                userDataFromStorage.name === currentUserInStoredAccounts.name 
                  ? '✅ Synced' 
                  : `❌ Not Synced (${userDataFromStorage.name} vs ${currentUserInStoredAccounts.name})`
              }
            </div>
            <div className={`p-2 rounded ${
              userDataFromStorage.username === currentUserInStoredAccounts.username 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              <strong>Username Sync:</strong> {
                userDataFromStorage.username === currentUserInStoredAccounts.username 
                  ? '✅ Synced' 
                  : `❌ Not Synced (${userDataFromStorage.username} vs ${currentUserInStoredAccounts.username})`
              }
            </div>
            <div className={`p-2 rounded ${
              userDataFromStorage.profile_image === currentUserInStoredAccounts.profileImage 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              <strong>Profile Image Sync:</strong> {
                userDataFromStorage.profile_image === currentUserInStoredAccounts.profileImage 
                  ? '✅ Synced' 
                  : `❌ Not Synced`
              }
            </div>
          </div>
        ) : (
          <p className="text-red-500">Cannot check sync status - missing data</p>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">How to Test</h3>
        <ol className="text-sm text-blue-700 space-y-1">
          <li>1. Go to your profile page and click "Edit Profile"</li>
          <li>2. Change your name, username, or profile image</li>
          <li>3. Save the changes</li>
          <li>4. Come back to this page and check if the sync status shows ✅ Synced</li>
          <li>5. The data should automatically refresh every 2 seconds</li>
        </ol>
      </div>

      {/* Manual Refresh Button */}
      <div className="mt-4 text-center">
        <button
          onClick={refreshData}
          className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
        >
          Manual Refresh
        </button>
      </div>
    </div>
  );
};

export default StoredAccountsTest;
