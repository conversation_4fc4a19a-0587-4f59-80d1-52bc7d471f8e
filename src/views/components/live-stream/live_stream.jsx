import React, { useState, useRef, useContext, useEffect } from "react";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
// import { SocketContext } from "../../../helpers/context/socket.js";
import { setApiMessage } from "../../../helpers/context/toaster";
import { liveSocket } from "../../../helpers/context/socket";
import WebRTCViewerGenerator from "./WebRTCViewerGenerator";
import Live from "../../../assets/images/svg_icon/live_icon.svg";
import video_icon from "../../../assets/images/svg_icon/video_cam.svg";
import Link from "../../../assets/images/svg_icon/link.svg";
import video from "../../../assets/images/svg_icon/video.svg";
import Send from "../../../assets/images/svg_icon/sned.svg";
import EmojiPicker from "emoji-picker-react";
import { MdOutlineEmojiEmotions } from "react-icons/md";
import { Popover, Typography, Button, Box } from "@mui/material";
import siteConstant from "../../../helpers/constant/siteConstant";

const LiveStreamApp = () => {
  const socket = liveSocket;
  const [roomId, setRoomId] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [stream, setStream] = useState(null);
  const [livedata, setLivedata] = useState([]);
  const [isViewingStream, setIsViewingStream] = useState(false);
  const [currentViewingUserId, setCurrentViewingUserId] = useState(null);
  const isViewingStreamRef = useRef(false);
  const currentViewingUserIdRef = useRef(null);
  const [remoteStreams, setRemoteStreams] = useState(new Set());
  const [viewerCount, setViewerCount] = useState(0);
  const [comments, setComments] = useState([]);
  const [hearts, setHearts] = useState(0);
  const [commentInput, setCommentInput] = useState("");
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const peerConnectionsRef = useRef({});
  const pendingIceCandidatesRef = useRef({});
  const remoteVideosRef = useRef({});
  const localVideoRef = useRef(null);
  const localStreamRef = useRef(null);
  const commentsContainerRef = useRef(null);
  const [remoteStream, setRemoteStream] = useState(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  // Camera and microphone control states
  const [isMuted, setIsMuted] = useState(false);
  const [isCameraOff, setIsCameraOff] = useState(false);

  // WebRTC viewer generator state
  const [showWebRTCViewer, setShowWebRTCViewer] = useState(false);

  // Permission error state to prevent flickering
  const [hasPermissionError, setHasPermissionError] = useState(false);

  const userId = localStorage.getItem("UserId");
  const emojiPickerRef = useRef(null);

  // Navigation Guard States
  const [showNavigationAlert, setShowNavigationAlert] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [showLeavePopover, setShowLeavePopover] = useState(false);
  const [confirmLeaveCallback, setConfirmLeaveCallback] = useState(null);

  const handlePopState = () => {
    if (isLive) {
      // Show popover instead of confirm
      setShowLeavePopover(true);

      // Save confirm action
      setConfirmLeaveCallback(() => () => {
        endLiveStream();
        window.history.back(); // Optional: go back after ending stream
      });

      // Prevent actual navigation
      window.history.pushState(null, "", window.location.pathname);
    }
  };

  // Navigation Guard Hook
  const useNavigationGuard = () => {
    const isLive = isStreaming && !isViewingStream;

    // Browser navigation protection (back button, refresh, close)
    useEffect(() => {
      const handleBeforeUnload = (event) => {
        if (isLive) {
          event.preventDefault();
          event.returnValue =
            "You are currently LIVE! Are you sure you want to leave the page?";
        }
      };

      const handlePopState = () => {
        if (isLive) {
          setShowLeavePopover(true);
          setConfirmLeaveCallback(() => () => {
            endLiveStream();
            window.history.back();
          });

          window.history.pushState(null, "", window.location.pathname);
        }
      };

      if (isLive) {
        window.addEventListener("beforeunload", handleBeforeUnload);
        window.addEventListener("popstate", handlePopState);
        window.history.pushState(null, "", window.location.pathname);
      }

      return () => {
        window.removeEventListener("beforeunload", handleBeforeUnload);
        window.removeEventListener("popstate", handlePopState);
      };
    }, [isLive]);

    // Function to handle navigation attempts
    const handleNavigation = (navigationCallback) => {
      if (isLive) {
        setPendingNavigation(() => navigationCallback);
        setShowNavigationAlert(true);
        return false; // Block navigation
      } else {
        navigationCallback(); // Allow navigation
        return true;
      }
    };

    return { handleNavigation };
  };

  // Use navigation guard hook
  const { handleNavigation } = useNavigationGuard();

  // Navigation Alert Handlers
  const handleConfirmNavigation = () => {
    // End live stream first
    endLiveStream();

    // Then execute pending navigation
    if (pendingNavigation) {
      setTimeout(() => {
        pendingNavigation();
      }, 500); // Small delay to ensure stream ends properly
    }

    setShowNavigationAlert(false);
    setPendingNavigation(null);
  };

  const handleCancelNavigation = () => {
    setShowNavigationAlert(false);
    setPendingNavigation(null);
  };

  // Example navigation functions (replace with your actual navigation logic)
  const navigateToDashboard = () => {
    handleNavigation(() => {
      window.location.href = "/dashboard";
    });
  };

  const navigateToAnalytics = () => {
    handleNavigation(() => {
      window.location.href = "/analytics";
    });
  };

  const navigateToPlanner = () => {
    handleNavigation(() => {
      window.location.href = "/planner";
    });
  };

  // Close on outside click
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target)
      ) {
        setShowEmojiPicker(false);
      }
    }

    if (showEmojiPicker) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showEmojiPicker]);

  // Get username from userData (stored as JSON)
  const getUserName = () => {
    try {
      const userData = JSON.parse(localStorage.getItem("userData"));
      return (
        userData?.name ||
        userData?.username ||
        localStorage.getItem("username") ||
        localStorage.getItem("name") ||
        localStorage.getItem("userName") ||
        "Anonymous"
      );
    } catch (error) {
      console.error("Error parsing userData:", error);
      return (
        localStorage.getItem("username") ||
        localStorage.getItem("name") ||
        localStorage.getItem("userName") ||
        "Anonymous"
      );
    }
  };

  const username = getUserName();

  // Debug log to check username
  // console.log("[LiveStream] Retrieved username:", username);
  // console.log("[LiveStream] UserData from localStorage:", localStorage.getItem("userData"));

  // Function to handle video orientation
  const applyVideoOrientation = (videoElement, isLocalStream = false) => {
    if (isLocalStream && isStreaming && !isViewingStream) {
      // For broadcaster's local video, flip horizontally to show mirror effect
      videoElement.style.transform = "scaleX(-1)";
    } else {
      // For remote videos or when viewing, keep normal orientation
      videoElement.style.transform = "none";
    }
  };

  // Toggle microphone function
  const toggleMute = () => {
    if (localStreamRef.current) {
      const newMutedState = !isMuted;
      setIsMuted(newMutedState);

      // Enable/disable audio tracks
      localStreamRef.current.getAudioTracks().forEach((track) => {
        track.enabled = !newMutedState;
        console.log(
          `[toggleMute] Audio track ${track.id} enabled: ${!newMutedState}`
        );
      });

      // Notify all peer connections about track state change
      Object.values(peerConnectionsRef.current).forEach((pc) => {
        if (pc && pc.connectionState === "connected") {
          console.log(
            `[toggleMute] Notifying peer connection about audio track state change`
          );
        }
      });

      console.log(
        `[toggleMute] Microphone ${newMutedState ? "muted" : "unmuted"}`
      );
    }
  };

  // Toggle camera function
  const toggleCamera = () => {
    if (localStreamRef.current) {
      const newCameraOffState = !isCameraOff;
      setIsCameraOff(newCameraOffState);

      // Enable/disable video tracks
      localStreamRef.current.getVideoTracks().forEach((track) => {
        track.enabled = !newCameraOffState;
        console.log(
          `[toggleCamera] Video track ${
            track.id
          } enabled: ${!newCameraOffState}`
        );
      });

      // Notify all peer connections about track state change
      Object.values(peerConnectionsRef.current).forEach((pc) => {
        if (pc && pc.connectionState === "connected") {
          console.log(
            `[toggleCamera] Notifying peer connection about video track state change`
          );
        }
      });

      // Emit camera status to server if broadcaster
      if (isStreaming && !isViewingStream && roomId && socket) {
        socket.emit("update_camera_status", {
          room: roomId,
          is_camera_on: newCameraOffState, // true if camera is OFF (to match Dart logic)
        });
        console.log(
          `[toggleCamera] Emitted update_camera_status:`,
          newCameraOffState
        );
      }

      console.log(
        `[toggleCamera] Camera ${
          newCameraOffState ? "turned off" : "turned on"
        }`
      );
    }
  };

  const iceConfiguration = {
    iceServers: [
      { urls: "stun:stun.l.google.com:19302" },
      { urls: "stun:stun1.l.google.com:19302" },
      { urls: "stun:stun2.l.google.com:19302" },
      { urls: "stun:stun3.l.google.com:19302" },
      { urls: "stun:stun4.l.google.com:19302" },
      {
        urls: "turn:openrelay.metered.ca:80",
        username: "openrelayproject",
        credential: "openrelayproject",
      },
      {
        urls: "turn:openrelay.metered.ca:443",
        username: "openrelayproject",
        credential: "openrelayproject",
      },
      {
        urls: "turn:openrelay.metered.ca:443?transport=tcp",
        username: "openrelayproject",
        credential: "openrelayproject",
      },
    ],
    iceCandidatePoolSize: 10,
  };

  // Function to send comment
  const sendComment = () => {
    if (!commentInput.trim() || !socket || !roomId) return;

    const comment = commentInput.trim();
    console.log("[sendComment] Sending comment:", comment);

    socket.emit("send_comment", {
      room: roomId,
      comment: comment,
      userId: userId,
      username: username,
      is_animated: false,
    });

    setCommentInput("");
    // Force scroll to bottom when user sends a comment
    setTimeout(() => {
      scrollToBottom();
    }, 50);
  };

  // Function to send emoji comment
  const sendEmojiComment = (emoji) => {
    if (!socket || !roomId) return;

    console.log("[sendEmojiComment] Sending emoji:", emoji);

    socket.emit("send_comment", {
      room: roomId,
      comment: emoji,
      userId: userId,
      username: username,
      is_animated: true,
    });

    // Force scroll to bottom when user sends an emoji
    setTimeout(() => {
      scrollToBottom();
    }, 50);
  };

  // Function to scroll to bottom of comments
  const scrollToBottom = () => {
    if (commentsContainerRef.current) {
      // Use smooth scrolling for better UX
      commentsContainerRef.current.scrollTo({
        top: commentsContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  // Auto-scroll to bottom when comments change
  useEffect(() => {
    // Add a small delay to ensure DOM is updated
    const timeoutId = setTimeout(() => {
      scrollToBottom();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [comments]);

  // Handle scroll events to show/hide scroll-to-bottom button
  const handleScroll = () => {
    if (commentsContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        commentsContainerRef.current;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 50;
      setShowScrollToBottom(!isNearBottom);
    }
  };

  const fetchLiveUsers = async () => {
    try {
      setIsLoading(true);
      const response = await apiInstance.get(URL.LIVE_USER);
      setLivedata(response.data.data);
      setIsLoading(false);
    } catch (error) {
      console.log("Error fetching live users:", error);
      setApiMessage("Failed to load live users");
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchLiveUsers();
  }, []);

  // Function to handle WebRTC reconnection
  const handleWebRTCReconnection = async (userId) => {
    console.log(
      `[handleWebRTCReconnection] Attempting to reconnect with ${userId}`
    );

    // Clean up existing connection
    if (peerConnectionsRef.current[userId]) {
      peerConnectionsRef.current[userId].close();
      delete peerConnectionsRef.current[userId];
    }

    // Clear pending ICE candidates
    if (pendingIceCandidatesRef.current[userId]) {
      pendingIceCandidatesRef.current[userId] = [];
    }

    // Create new peer connection
    const pc = createPeerConnection(userId);

    // If we're the broadcaster, create a new offer
    if (isStreaming && !isViewingStream) {
      try {
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);

        socket.emit("offer", {
          offer: {
            type: offer.type,
            sdp: offer.sdp,
          },
          userId: userId,
          room: roomId,
        });

        console.log(`[handleWebRTCReconnection] Sent new offer to ${userId}`);
      } catch (error) {
        console.error(
          `[handleWebRTCReconnection] Error creating new offer:`,
          error
        );
      }
    }
  };

  const setStatus = (message, isError = false) => {
    setApiMessage(message);
    if (isError) {
      console.error(message);
    }
    if (!isError && message) {
      setTimeout(() => {
        setApiMessage("");
      }, 3000);
    }
  };

  const createPeerConnection = (userId) => {
    if (peerConnectionsRef.current[userId]) {
      console.log("Peer connection already exists for:", userId);
      return peerConnectionsRef.current[userId];
    }

    const pc = new RTCPeerConnection(iceConfiguration);

    pc.onicecandidate = (event) => {
      if (event.candidate) {
        // Match mobile app's ICE candidate format
        socket.emit("ice_candidate", {
          candidate: {
            candidate: event.candidate.candidate,
            sdpMid: event.candidate.sdpMid,
            sdpMLineIndex: event.candidate.sdpMLineIndex,
          },
          userId: userId,
          room: roomId,
        });
      }
    };

    pc.oniceconnectionstatechange = () => {
      console.log(
        `[PeerConnection] ICE state for ${userId}:`,
        pc.iceConnectionState
      );

      // Handle connection state changes
      if (pc.iceConnectionState === "connected") {
        console.log(`[PeerConnection] Successfully connected to ${userId}`);
      } else if (pc.iceConnectionState === "failed") {
        console.error(`[PeerConnection] Connection failed for ${userId}`);
        setStatus(`Connection failed for ${userId}`, true);
      } else if (pc.iceConnectionState === "disconnected") {
        console.warn(`[PeerConnection] Connection disconnected for ${userId}`);
      }
    };

    pc.onconnectionstatechange = () => {
      console.log(
        `[PeerConnection] Connection state for ${userId}:`,
        pc.connectionState
      );

      if (pc.connectionState === "connected") {
        console.log(
          `[PeerConnection] Peer connection established with ${userId}`
        );
      } else if (pc.connectionState === "failed") {
        console.error(`[PeerConnection] Peer connection failed with ${userId}`);
        setStatus(`Peer connection failed with ${userId}`, true);

        // Attempt reconnection after a delay
        setTimeout(() => {
          handleWebRTCReconnection(userId);
        }, 2000);
      } else if (pc.connectionState === "disconnected") {
        console.warn(
          `[PeerConnection] Peer connection disconnected with ${userId}`
        );

        // Attempt reconnection after a delay
        setTimeout(() => {
          handleWebRTCReconnection(userId);
        }, 1000);
      }
    };

    pc.onsignalingstatechange = () => {
      console.log(
        `[PeerConnection] Signaling state for ${userId}:`,
        pc.signalingState
      );
    };

    pc.ontrack = (event) => {
      console.log(
        `[ontrack] Remote track received (userId: ${userId}, isViewingStream: ${isViewingStream}, isStreaming: ${isStreaming})`,
        event.streams[0]
      );

      if (event.streams && event.streams.length > 0) {
        const streamId = event.streams[0].id;
        console.log(
          `[ontrack] Stream ID: ${streamId}, Track kind: ${event.track.kind}`
        );

        if (!remoteStreams.has(streamId)) {
          setRemoteStreams((prev) => new Set([...prev, streamId]));

          // For viewers, set the remote stream for playback
          if (isViewingStream) {
            console.log(
              `[ontrack][VIEWER] Setting remoteStream for userId: ${userId}`
            );
            setRemoteStream(event.streams[0]);

            // Ensure the video element is properly set up
            if (localVideoRef.current) {
              localVideoRef.current.srcObject = event.streams[0];
              localVideoRef.current.play().catch((e) => {
                console.error("[ontrack] Error playing remote video:", e);
              });
            }
          } else {
            console.log(
              `[ontrack][BROADCASTER] Received remote track from viewer userId: ${userId}`
            );
          }
        } else {
          console.log(`[ontrack] Stream ${streamId} already exists, skipping`);
        }
      } else {
        console.warn("[ontrack] No streams in track event");
      }
    };

    // Only add local stream tracks if this is the broadcaster (room creator)
    if (localStreamRef.current && isStreaming && !isViewingStream) {
      const tracks = localStreamRef.current.getTracks();
      console.log("[createPeerConnection] Tracks to add:", tracks);
      tracks.forEach((track) => {
        pc.addTrack(track, localStreamRef.current);
        console.log(
          `[createPeerConnection] Broadcaster adding track:`,
          track.kind,
          track.id
        );
      });
    }

    peerConnectionsRef.current[userId] = pc;
    console.log(
      "[createPeerConnection] Created new peer connection for:",
      userId
    );
    return pc;
  };

  const addRemoteVideo = (userId, streamId, videoElement) => {
    if (!remoteVideosRef.current[userId]) {
      remoteVideosRef.current[userId] = {};
    }
    remoteVideosRef.current[userId][streamId] = videoElement;
    setViewerCount(Object.keys(remoteVideosRef.current).length);
  };

  const removeRemoteVideo = (userId, streamId) => {
    if (
      remoteVideosRef.current[userId] &&
      remoteVideosRef.current[userId][streamId]
    ) {
      delete remoteVideosRef.current[userId][streamId];
    }
    if (Object.keys(remoteVideosRef.current[userId] || {}).length === 0) {
      delete remoteVideosRef.current[userId];
    }
    setViewerCount(Object.keys(remoteVideosRef.current).length);
  };

  const stopExistingStream = () => {
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach((track) => track.stop());
      localStreamRef.current = null;
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = null;
      }
    }

    Object.entries(remoteVideosRef.current).forEach(([userId, streams]) => {
      Object.entries(streams).forEach(([streamId, videoElement]) => {
        if (videoElement) {
          const video = videoElement.querySelector("video");
          if (video) {
            video.srcObject = null;
          }
          videoElement.remove();
          removeRemoteVideo(userId, streamId);
        }
      });
    });

    Object.values(peerConnectionsRef.current).forEach((pc) => {
      if (pc) {
        pc.close();
      }
    });
    peerConnectionsRef.current = {};
    setRemoteStreams(new Set());
    setIsStreaming(false);
    setIsViewingStream(false);
    setStream(null);
    setComments([]);
    setViewerCount(0);
    setRemoteStream(null);

    // Reset camera and microphone states
    setIsMuted(false);
    setIsCameraOff(false);

    // Reset permission error state
    setHasPermissionError(false);
  };

  const cleanupUser = (userId) => {
    if (remoteVideosRef.current[userId]) {
      Object.entries(remoteVideosRef.current[userId]).forEach(
        ([streamId, videoElement]) => {
          if (videoElement) {
            const video = videoElement.querySelector("video");
            if (video) {
              video.srcObject = null;
            }
            videoElement.remove();
            removeRemoteVideo(userId, streamId);
          }
        }
      );
    }

    if (peerConnectionsRef.current[userId]) {
      peerConnectionsRef.current[userId].close();
      delete peerConnectionsRef.current[userId];
    }

    // Request updated user count after cleanup
    if (socket && roomId) {
      setTimeout(() => {
        socket.emit("current_users_number", { room: roomId });
      }, 1000);
    }
  };

  useEffect(() => {
    if (stream && localVideoRef.current && isStreaming && !isViewingStream) {
      const videoElement = localVideoRef.current;
      videoElement.srcObject = stream;

      const handleLoadedMetadata = () => {
        if (videoElement && !videoElement.paused) {
          videoElement.play().catch((e) => {
            console.error("Error playing video:", e);
            setStatus(`Error playing video: ${e.message}`, true);
          });
        }
        // Apply orientation after metadata is loaded
        applyVideoOrientation(videoElement, true);
      };

      videoElement.onloadedmetadata = handleLoadedMetadata;

      return () => {
        if (videoElement) {
          videoElement.onloadedmetadata = null;
          videoElement.srcObject = null;
        }
      };
    }
  }, [stream, isStreaming, isViewingStream]);

  // Effect for viewers to play remote stream
  useEffect(() => {
    if (isViewingStream && localVideoRef.current && remoteStream) {
      console.log("[VideoEffect][VIEWER] Setting remote stream for viewer");
      localVideoRef.current.srcObject = remoteStream;

      const handleLoadedMetadata = () => {
        console.log("[VideoEffect][VIEWER] Remote stream metadata loaded");
        if (localVideoRef.current && !localVideoRef.current.paused) {
          localVideoRef.current.play().catch((e) => {
            console.error("Error playing remote video:", e);
            setStatus(`Error playing remote video: ${e.message}`, true);
          });
        }
        // Apply orientation for viewer (should be normal, not flipped)
        applyVideoOrientation(localVideoRef.current, false);
      };

      const handleCanPlay = () => {
        console.log("[VideoEffect][VIEWER] Remote stream can play");
      };

      const handleError = (e) => {
        console.error("[VideoEffect][VIEWER] Remote stream error:", e);
      };

      localVideoRef.current.onloadedmetadata = handleLoadedMetadata;
      localVideoRef.current.oncanplay = handleCanPlay;
      localVideoRef.current.onerror = handleError;

      return () => {
        if (localVideoRef.current) {
          localVideoRef.current.onloadedmetadata = null;
          localVideoRef.current.oncanplay = null;
          localVideoRef.current.onerror = null;
          localVideoRef.current.srcObject = null;
        }
      };
    }
  }, [isViewingStream, remoteStream]);

  // Effect to handle video orientation when streaming state changes
  useEffect(() => {
    if (localVideoRef.current) {
      applyVideoOrientation(localVideoRef.current, !isViewingStream);
    }
  }, [isStreaming, isViewingStream]);

  // Effect to periodically request user count when streaming or viewing
  useEffect(() => {
    if (!socket || !roomId || (!isStreaming && !isViewingStream)) return;

    // Request user count immediately
    socket.emit("current_users_number", { room: roomId });

    // Set up periodic requests every 5 seconds
    const intervalId = setInterval(() => {
      if (socket && roomId) {
        socket.emit("current_users_number", { room: roomId });
      }
    }, 5000);

    return () => {
      clearInterval(intervalId);
    };
  }, [socket, roomId, isStreaming, isViewingStream]);

  useEffect(() => {
    console.log("[useEffect] Setting up socket listeners");
    if (!liveSocket) {
      console.error("Socket not initialized");
      setStatus("Socket connection not established", true);
      return;
    }

    const handleConnect = () => {
      console.log("[socket] handleConnect:", socket.id);
      setIsConnected(true);
      setApiMessage("");
    };

    const handleDisconnect = () => {
      console.log("[socket] handleDisconnect");
      setIsConnected(false);
      setStatus("Disconnected from server", true);

      // If viewing a stream and socket disconnects, treat it as stream ended
      if (isViewingStream) {
        setApiMessage(
          "warning",
          "Connection lost. The live stream may have ended."
        );

        // Clean up and return to main screen
        stopExistingStream();
        setRoomId("");
        setCurrentViewingUserId(null);
        setIsViewingStream(false);
        isViewingStreamRef.current = false;
        setComments([]);
        setCommentInput("");
        setRemoteStream(null);
      } else {
        stopExistingStream();
      }
    };

    const handleConnectError = (error) => {
      console.error("[socket] handleConnectError:", error);
      setIsConnected(false);
      setStatus(`Connection error: ${error.message || "Unknown error"}`, true);

      // If viewing a stream and connection error occurs, treat it as stream ended
      if (isViewingStream) {
        setApiMessage(
          "error",
          "Connection error. The live stream may have ended."
        );

        // Clean up and return to main screen
        stopExistingStream();
        setRoomId("");
        setCurrentViewingUserId(null);
        setIsViewingStream(false);
        isViewingStreamRef.current = false;
        setComments([]);
        setCommentInput("");
        setRemoteStream(null);
      }
    };

    const handleRoomCreated = (data) => {
      console.log("[socket] handleRoomCreated:", data);
      setStatus(`Room '${data.room}' created successfully!`);
    };

    const handleUserJoined = async (data) => {
      console.log(
        "[socket] handleUserJoined:",
        data,
        "isStreaming:",
        isStreaming,
        "isViewingStream:",
        isViewingStream,
        "userId:",
        userId
      );
      // Only broadcaster should handle this
      if (data.room === roomId && isStreaming && !isViewingStream) {
        console.log(
          "[handleUserJoined] Broadcaster creating offer for viewer:",
          data.userId
        );
        const pc = createPeerConnection(data.userId); // data.userId is the viewer
        try {
          const offer = await pc.createOffer();
          await pc.setLocalDescription(offer);
          console.log(
            "[handleUserJoined] Broadcaster created offer for viewer",
            data.userId,
            offer
          );

          // Match mobile app's offer format
          socket.emit("offer", {
            offer: {
              type: offer.type,
              sdp: offer.sdp,
            },
            userId: data.userId, // send to viewer
            room: roomId,
          });
        } catch (error) {
          console.error("[handleUserJoined] Error creating offer:", error);
          setStatus("Failed to establish connection with viewer", true);
        }
      }

      // Request updated user count when someone joins
      if (socket && roomId) {
        setTimeout(() => {
          socket.emit("current_users_number", { room: roomId });
        }, 500);
      }
    };

    const handleOffer = async (data) => {
      console.log(
        "[socket] handleOffer:",
        data,
        "isViewingStream:",
        isViewingStream,
        "currentViewingUserId:",
        currentViewingUserId
      );
      // Only viewer should handle this
      if (data.room === roomId && isViewingStreamRef.current) {
        // data.userId is the broadcaster's userId
        const broadcasterId = data.userId;
        console.log(
          "[handleOffer] Received offer from broadcaster:",
          broadcasterId,
          data.offer
        );
        const pc = createPeerConnection(broadcasterId);

        try {
          console.log("[handleOffer] Before setRemoteDescription");
          // Handle both formats: direct offer object or nested offer object
          const offerToSet = data.offer.sdp ? data.offer : data.offer.offer;
          await pc.setRemoteDescription(offerToSet);
          console.log("[handleOffer] After setRemoteDescription");

          // Process pending ICE candidates after setting remote description
          const pending = pendingIceCandidatesRef.current[broadcasterId];
          if (pending && pending.length > 0) {
            for (const candidate of pending) {
              const iceCandidate = new RTCIceCandidate(candidate);
              await pc.addIceCandidate(iceCandidate);
            }
            pendingIceCandidatesRef.current[broadcasterId] = [];
          }

          console.log("[handleOffer] Before createAnswer");
          const answer = await pc.createAnswer();
          console.log("[handleOffer] After createAnswer", answer);
          console.log("[handleOffer] Before setLocalDescription");
          await pc.setLocalDescription(answer);
          console.log(
            "[handleOffer] setLocalDescription(answer) complete",
            answer
          );
          console.log(
            "[handleOffer] Viewer created answer for broadcaster",
            broadcasterId,
            answer
          );

          // Match mobile app's answer format
          socket.emit("answer", {
            answer: {
              type: answer.type,
              sdp: answer.sdp,
            },
            userId: broadcasterId, // send to broadcaster
            room: roomId,
          });
        } catch (error) {
          console.error(
            "[handleOffer] Error after setRemoteDescription or setLocalDescription or createAnswer:",
            error
          );
          setStatus("Failed to establish connection with broadcaster", true);
        }
      }
    };

    const handleAnswer = async (data) => {
      console.log(
        "[socket] handleAnswer:",
        data,
        "isStreaming:",
        isStreaming,
        "isViewingStream:",
        isViewingStream,
        "userId:",
        userId
      );
      if (data.room === roomId && isStreaming && !isViewingStream) {
        try {
          const pc = peerConnectionsRef.current[data.userId];
          if (pc) {
            // Only set remote description if in the correct state
            if (pc.signalingState === "have-local-offer") {
              // Handle both formats: direct answer object or nested answer object
              const answerToSet = data.answer.sdp
                ? data.answer
                : data.answer.answer;
              await pc.setRemoteDescription(answerToSet);
              console.log(
                "[handleAnswer] Broadcaster set remote description from viewer",
                data.userId,
                answerToSet
              );
              // Process pending ICE candidates after setting remote description
              const pending = pendingIceCandidatesRef.current[data.userId];
              if (pending && pending.length > 0) {
                for (const candidate of pending) {
                  const iceCandidate = new RTCIceCandidate(candidate);
                  await pc.addIceCandidate(iceCandidate);
                }
                pendingIceCandidatesRef.current[data.userId] = [];
              }
            } else {
              console.warn(
                `[handleAnswer] Skipping setRemoteDescription: signalingState is ${pc.signalingState}`
              );
            }
          }
        } catch (error) {
          console.error("[handleAnswer] Error handling answer:", error);
        }
      }
    };

    const handleIceCandidate = async (data) => {
      console.log(
        "[socket] handleIceCandidate:",
        data,
        "isStreaming:",
        isStreaming,
        "isViewingStream:",
        isViewingStream,
        "userId:",
        userId,
        "currentViewingUserId:",
        currentViewingUserId
      );
      if (data.room === roomId) {
        try {
          const pc = peerConnectionsRef.current[data.userId];
          if (pc && pc.remoteDescription) {
            // Handle mobile app's ICE candidate format
            const candidateData = data.candidate.candidate
              ? data.candidate
              : data.candidate;
            const iceCandidate = new RTCIceCandidate(candidateData);
            await pc.addIceCandidate(iceCandidate);
          } else if (pc) {
            if (!pendingIceCandidatesRef.current[data.userId]) {
              pendingIceCandidatesRef.current[data.userId] = [];
            }
            // Store the candidate in the same format it was received
            pendingIceCandidatesRef.current[data.userId].push(data.candidate);
          }
        } catch (error) {
          console.error(
            "[handleIceCandidate] Error adding ICE candidate:",
            error
          );
        }
      }
    };

    const handleUserLeft = (data) => {
      console.log("[socket] handleUserLeft:", data);
      if (data.userId) {
        cleanupUser(data.userId);
      }

      // Request updated user count when someone leaves
      if (socket && roomId) {
        setTimeout(() => {
          socket.emit("current_users_number", { room: roomId });
        }, 500);
      }
    };

    const handleStreamEnded = (data) => {
      console.log("[socket] handleStreamEnded:", data);
      if (isViewingStream && data.userId === currentViewingUserId) {
        setStatus("The live stream has ended", true);
        stopExistingStream();
        setRoomId("");
        setCurrentViewingUserId(null);
        setApiMessage("info", "The live stream has ended");
      }
    };

    const handleRoomClosed = (data) => {
      console.log("[socket] handleRoomClosed:", data);
      if (data.room === roomId && isViewingStream) {
        // Show toast notification
        setApiMessage("warning", "The live has ended by the host.");

        // Clean up and return to main screen
        stopExistingStream();
        setRoomId("");
        setCurrentViewingUserId(null);
        setIsViewingStream(false);
        isViewingStreamRef.current = false;
        setComments([]);
        setCommentInput("");
        setRemoteStream(null);

        // Show status message
        setStatus("The live stream has ended by the host", true);
      }
    };

    const handleCommentList = (data) => {
      console.log("[socket] handleCommentList:", data);
      if (data.room === roomId) {
        setComments([]);
        if (data.comments && Array.isArray(data.comments)) {
          const formattedComments = data.comments.map((comment) => ({
            message: comment.comment || comment.message,
            username: comment.username,
            timestamp: comment.timestamp || new Date(),
            isOwnMessage: false, // Always show on left side
          }));
          setComments(formattedComments);
          // Scroll to bottom when comments are loaded
          setTimeout(() => {
            scrollToBottom();
          }, 200);
        }
      }
    };

    const handleNewComment = (data) => {
      console.log("[socket] handleNewComment:", data);
      if (data.room === roomId) {
        setComments((prev) => [
          ...prev,
          {
            message: data.comment,
            username: data.username,
            timestamp: data.timestamp || new Date(),
            isOwnMessage: false, // Always show on left side
          },
        ]);
      }
    };

    const handleCurrentUsersNumber = (data) => {
      console.log("[socket] handleCurrentUsersNumber:", data);
      if (data.room === roomId) {
        // Subtract 2 to account for broadcaster and adjust for accurate viewer count
        const viewerCount = data.count > 0 ? data.count - 2 : 0;
        setViewerCount(Math.max(0, viewerCount));
      }
    };

    const handleCameraStatus = (data) => {
      console.log("[socket] camera_status:", data);
      if (data.room === roomId) {
        setIsCameraOff(data.is_camera_on); // true means camera is OFF (to match Dart logic)
      }
    };

    if (socket.connected) {
      setIsConnected(true);
    }

    socket.on("connect", handleConnect);
    socket.on("disconnect", handleDisconnect);
    socket.on("connect_error", handleConnectError);
    socket.on("room_created", handleRoomCreated);
    socket.on("user_joined", handleUserJoined);
    socket.on("offer", handleOffer);
    socket.on("answer", handleAnswer);
    socket.on("ice_candidate", handleIceCandidate);
    socket.on("user_left", handleUserLeft);
    socket.on("stream_ended", handleStreamEnded);
    socket.on("room_closed", handleRoomClosed);
    socket.on("comment_list", handleCommentList);
    socket.on("new_comment", handleNewComment);
    socket.on("current_users_number", handleCurrentUsersNumber);
    socket.on("camera_status", handleCameraStatus);

    return () => {
      socket.off("connect", handleConnect);
      socket.off("disconnect", handleDisconnect);
      socket.off("connect_error", handleConnectError);
      socket.off("room_created", handleRoomCreated);
      socket.off("user_joined", handleUserJoined);
      socket.off("offer", handleOffer);
      socket.off("answer", handleAnswer);
      socket.off("ice_candidate", handleIceCandidate);
      socket.off("user_left", handleUserLeft);
      socket.off("stream_ended", handleStreamEnded);
      socket.off("room_closed", handleRoomClosed);
      socket.off("comment_list", handleCommentList);
      socket.off("new_comment", handleNewComment);
      socket.off("current_users_number", handleCurrentUsersNumber);
      socket.off("camera_status", handleCameraStatus);
    };
  }, [
    socket,
    userId,
    currentViewingUserId,
    isStreaming,
    isViewingStream,
    roomId,
  ]);

  const createRoom = async () => {
    console.log("[createRoom] userId:", userId, "roomId:", `liv-${userId}`);
    if (!socket || !isConnected) {
      setStatus("Socket connection not established", true);
      return;
    }

    if (!userId) {
      setStatus("User ID not found. Please login first.", true);
      return;
    }

    // Check if getUserMedia is supported
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      setStatus(
        "Your browser doesn't support camera and microphone access",
        true
      );
      return;
    }

    // Reset permission error state when trying again
    setHasPermissionError(false);
    setIsLoading(true);
    setRoomId(`liv-${userId}`);
    stopExistingStream();
    setComments([]); // Clear previous comments
    setCommentInput(""); // Clear comment input

    // Reset camera and microphone states for new stream
    setIsMuted(false);
    setIsCameraOff(false);

    try {
      const constraints = {
        video: {
          width: { min: 320, ideal: 640, max: 1280 },
          height: { min: 240, ideal: 480, max: 720 },
          facingMode: "user",
          frameRate: { ideal: 30, max: 30 },
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: { ideal: 48000 },
          channelCount: { ideal: 2 },
        },
      };

      const mediaStream = await navigator.mediaDevices.getUserMedia(
        constraints
      );
      localStreamRef.current = mediaStream;
      setStream(mediaStream);
      setIsStreaming(true);

      console.log("[createRoom] emitting create_room", `liv-${userId}`);
      socket.emit("create_room", { room: `liv-${userId}` });
      setApiMessage("success", "Live stream Started successfully!");

      // Emit join_room for the creator as well, matching Dart flow
      console.log("[createRoom] emitting join_room", `liv-${userId}`);
      socket.emit("join_room", { room: `liv-${userId}` });

      // Request current user count for the room
      console.log(
        "[createRoom] requesting current_users_number",
        `liv-${userId}`
      );
      socket.emit("current_users_number", { room: `liv-${userId}` });

      try {
        await apiInstance.post(URL.START_LIVE, { userId });
      } catch (error) {
        console.error("Failed to update live status:", error);
      }

      setStatus("You are now live streaming!");
      setIsLoading(false);
    } catch (error) {
      console.error("Error accessing media devices:", error);

      // Check if it's a permission error
      if (
        error.name === "NotAllowedError" ||
        error.name === "PermissionDeniedError"
      ) {
        setHasPermissionError(true);
        setStatus(
          "Camera and microphone permissions are required. Please allow access and try again.",
          true
        );
      } else if (error.name === "NotFoundError") {
        setStatus(
          "No camera or microphone found. Please connect a device and try again.",
          true
        );
      } else if (error.name === "NotReadableError") {
        setStatus(
          "Camera or microphone is already in use by another application.",
          true
        );
      } else if (error.name === "OverconstrainedError") {
        setStatus(
          "Camera or microphone doesn't meet the required specifications.",
          true
        );
      } else {
        setStatus(
          "Error accessing camera and microphone! " + error.message,
          true
        );
      }

      setIsLoading(false);
    }
  };

  const joinLiveStream = async (targetUserId) => {
    console.log("[joinLiveStream] called with:", targetUserId);
    console.log(
      "[joinLiveStream] userId:",
      userId,
      "currentViewingUserId:",
      currentViewingUserId,
      "roomId:",
      `liv-${targetUserId}`
    );
    if (!socket || !isConnected) {
      setStatus("Socket connection not established", true);
      return;
    }

    if (!userId) {
      setStatus("User ID not found. Please login first.", true);
      return;
    }

    if (targetUserId === userId) {
      setStatus("You cannot join your own live stream", true);
      return;
    }

    setIsLoading(true);
    stopExistingStream();
    setCurrentViewingUserId(targetUserId);
    currentViewingUserIdRef.current = targetUserId;
    setIsViewingStream(true);
    isViewingStreamRef.current = true;
    setRoomId(`liv-${targetUserId}`);
    setComments([]);
    setCommentInput("");

    try {
      // Viewers don't need local stream - they only receive remote stream
      setStream(null);
      setRemoteStream(null);

      console.log(
        "[joinLiveStream] emitting check_room",
        `liv-${targetUserId}`
      );
      socket.emit("check_room", { room: `liv-${targetUserId}` });
      console.log("[joinLiveStream] emitting join_room", `liv-${targetUserId}`);
      socket.emit("join_room", { room: `liv-${targetUserId}` });
      console.log(
        "[joinLiveStream] emitting get_comment_list",
        `liv-${targetUserId}`
      );
      socket.emit("get_comment_list", { room: `liv-${targetUserId}` });

      // Request current user count for the room
      console.log(
        "[joinLiveStream] requesting current_users_number",
        `liv-${targetUserId}`
      );
      socket.emit("current_users_number", { room: `liv-${targetUserId}` });

      setStatus(`Joined ${targetUserId}'s live stream`);
      setIsLoading(false);
    } catch (error) {
      console.error("Error joining room:", error);
      setStatus("Failed to join live stream. Please try again.", true);
      setIsLoading(false);
    }
  };

  const endLiveStream = async () => {
    if (socket && roomId) {
      if (isStreaming && !isViewingStream) {
        // Broadcaster ending the stream
        console.log("[endLiveStream] Broadcaster ending stream, room:", roomId);
        socket.emit("close_room", { room: roomId });
        setApiMessage("info", "Live stream ended successfully!");
        try {
          await apiInstance.post(URL.END_LIVE, { userId });
        } catch (error) {
          console.error("Failed to update live status:", error);
        }
      } else {
        // Viewer leaving the stream
        console.log("[endLiveStream] Viewer leaving stream, room:", roomId);
        socket.emit("leave_stream", { room: roomId });
      }
    }
    stopExistingStream();
    setRoomId("");
    setCurrentViewingUserId(null);
    setIsViewingStream(false);
    isViewingStreamRef.current = false;
    setComments([]);
    setCommentInput("");
    setRemoteStream(null);
    setViewerCount(0);
    setStatus(isStreaming ? "Live stream ended" : "Left the stream");
  };

  return (
    <div className="min-h-screen overflow-y-auto font-Ubuntu mb-20 sm:mb-24 lg:mb-5 xl:mb-16 2xl:mb-20 pt-5 ">
      <Popover
        open={showLeavePopover}
        anchorReference="anchorPosition"
        anchorPosition={{
          top: window.innerHeight / 2 - 100,
          left: window.innerWidth / 2 - 150,
        }}
        onClose={() => setShowLeavePopover(false)}
        PaperProps={{
          sx: {
            padding: 5,
            borderRadius: 3,
            maxWidth: 400,
            textAlign: "center",
          },
        }}
      >
        <p className="pb-2">End your live stream?</p>
        <p>If you end your live,it'll also end for all of your viewers.</p>
        <div className="flex justify-around items-center mt-3">
          <div
            onClick={() => setShowLeavePopover(false)}
            className="px-10 py-1.5 bg-gray-200 rounded-[9px] text-Red cursor-pointer"
          >
            Stay
          </div>
          <div
            className="px-10 py-1.5 bg-Red rounded-[9px] text-white cursor-pointer"
            onClick={() => {
              setShowLeavePopover(false);
              if (confirmLeaveCallback) confirmLeaveCallback();
            }}
          >
            Leave
          </div>
        </div>
      </Popover>

      <main className="max-w-[1500px] mx-auto flex flex-col lg:flex-row gap-4 sm:gap-6 lg:gap-8 ">
        {/* Main Video/Card Area */}
        <section className="flex-1 w-full mx-auto order-2 lg:order-1 bg-white shadow-lg rounded-[12px] pt-2">
          {/* Not streaming or viewing: Show start and live users */}
          {!isStreaming && !isViewingStream ? (
            <div className="flex flex-col gap-4 sm:gap-6 lg:gap-8 ">
              {/* Start Stream Card */}
              <div className="pt-4 sm:pt-6 lg:pt-8 px-4 sm:px-6 lg:px-8 flex flex-row items-center text-center gap-4 md:gap-6">
                <div className="px-4">
                  <img src={Live} className="img-fluid" />
                </div>
                <div className="flex flex-col items-start">
                  <p className="text-base sm:text-[30px] lg:text-[45px] 2xl:text-[50px] text-black font-bold sm:mb-9 whitespace-nowrap">
                    Ready to Go
                  </p>
                  <span className="flex justify-start text-base sm:text-[30px] lg:text-[45px] 2xl:text-[50px] text-black font-bold sm:mb-6 whitespace-nowrap">
                    Live?
                  </span>
                  <p className="text-[13px] sm:text-[14px] lg:text-[20px] text-black mb-4 sm:mb-6 md:whitespace-nowrap sm:ms-1 w-[200px] sm:w-full text-start">
                    Share real-time moments with your followers.
                  </p>
                  <div
                    onClick={createRoom}
                    disabled={
                      !isConnected || isLoading || !userId || hasPermissionError
                    }
                    className={`flex items-center gap-2 px-6 sm:px-6 lg:px-16 2xl:px-28 py-2 sm:py-3 rounded-[12px] text-sm sm:text-base lg:text-lg font-bold shadow-lg transition-all duration-200 ${
                      isConnected && !isLoading && userId && !hasPermissionError
                        ? "bg-Red text-white cursor-pointer"
                        : "bg-gray-300 text-gray-400 cursor-not-allowed"
                    }`}
                  >
                    {isLoading ? (
                      <svg
                        className="animate-spin h-4 w-4 sm:h-5 sm:w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                      </svg>
                    ) : (
                      <>
                        <p className="hidden sm:inline ">Start Live Now</p>
                        <span className="sm:hidden">Live</span>
                      </>
                    )}
                  </div>
                </div>
                {!userId && (
                  <p className="text-xs sm:text-sm text-[#d84727] mt-2 sm:mt-3">
                    User ID not found. Please login first.
                  </p>
                )}
                {hasPermissionError && (
                  <div className="mt-2 sm:mt-3 p-2 sm:p-3 bg-[#FFF3F3] border border-[#d84727] rounded-lg w-full max-w-sm">
                    <p className="text-xs sm:text-sm text-[#d84727] mb-2">
                      Camera and microphone permissions are required to go live.
                    </p>
                    <button
                      onClick={() => {
                        setHasPermissionError(false);
                        setStatus("");
                      }}
                      className="text-xs sm:text-sm text-[#d84727] underline hover:no-underline"
                    >
                      Try again
                    </button>
                  </div>
                )}
              </div>

              {/* Live Users Grid */}
              <div className="rounded-xl sm:rounded-2xl p-3 sm:p-4 mx-4  ">
                <h2 className="text-base sm:text-lg md:text-xl font-bold text-Red mb-3 sm:mb-4 flex items-center gap-2">
                  <span className="w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full animate-pulse"></span>
                  Currently Live
                </h2>
                {isLoading ? (
                  <div className="flex justify-center items-center py-6 sm:py-8">
                    <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-[#674941]"></div>
                    <span className="ml-2 text-sm sm:text-base text-[#674941]">
                      Loading streams...
                    </span>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 sm:gap-4 lg:gap-6 mb-6">
                    {livedata?.length > 0 ? (
                      livedata.map((user) => (
                        <div
                          key={user.user_id}
                          className="group border cursor-pointer flex flex-col p-3 sm:p-4 relative rounded-[12px] hover:scale-105 transition-all ease-in delay-90 "
                        >
                          <div className="flex justify-center items-center flex-col gap-2">
                            <img
                              src={
                                user.userprofile
                                  ? `${URL.SOCKET_URL}${user.userprofile}`
                                  : siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                              }
                              alt={user.username || "Unknown"}
                              className="h-[120px] sm:h-[120px] w-[120px] rounded-full object-cover"
                              onError={(e) => {
                                e.target.onerror = null;
                                e.target.src =
                                  siteConstant.SOCIAL_ICONS.DUMMY_PROFILE;
                                e.target.className =
                                  "h-[80px] w-[80px] border rounded-full p-2";
                              }}
                            />
                            <span className="font-semibold text-[#563D39] truncate w-full text-xs sm:text-sm">
                              {user.username || "Unknown"}
                            </span>
                          </div>

                          <span className="text-[10px] sm:text-xs text-[#EB5757] mt-1">
                            LIVE
                          </span>
                          <div className="flex gap-1 sm:gap-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <button
                              onClick={() => joinLiveStream(user.user_id)}
                              className="px-2 sm:px-3 lg:px-4 py-1 rounded-[9px] bg-[#EB5757] text-white text-xs font-bold "
                            >
                              Join Now
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                const streamLink = `${window.location.origin}/u/liv-${user.user_id}`;
                                navigator.clipboard
                                  .writeText(streamLink)
                                  .then(() => {
                                    setApiMessage(
                                      "success",
                                      "Universal stream link copied to clipboard!"
                                    );
                                  })
                                  .catch(() => {
                                    setApiMessage(
                                      "error",
                                      "Failed to copy link"
                                    );
                                  });
                              }}
                              className="px-1 sm:px-2 py-1 bg-Red text-white text-xs font-bold rounded-[9px]"
                              title="Copy universal stream link"
                            >
                              Copy Link
                            </button>
                            {/* <button
                              onClick={(e) => {
                                e.stopPropagation();
                                const webrtcUrl = `${window.location.origin}/webrtc-viewer.html?stream=liv-${user.user_id}`;
                                navigator.clipboard
                                  .writeText(webrtcUrl)
                                  .then(() => {
                                    setApiMessage(
                                      "success",
                                      "WebRTC viewer link copied to clipboard!"
                                    );
                                  })
                                  .catch(() => {
                                    setApiMessage(
                                      "error",
                                      "Failed to copy WebRTC link"
                                    );
                                  });
                              }}
                              className="px-1 sm:px-2 py-1 rounded-full bg-[#563D39] text-white text-xs font-bold shadow hover:scale-105 transition-all"
                              title="Copy WebRTC viewer link"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-3 w-3"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 10l4.553-2.276A1 1 0 0010 8.618v6.764a1 1 0 001.447.894L5 14"
                                />
                              </svg>
                            </button> */}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="col-span-full flex flex-col items-center py-8 sm:pb-12">
                        <img
                          src="/src/assets/images/datanotfound.svg"
                          alt="No live"
                          className="w-16 h-16 sm:w-20 sm:h-20 md:w-32 md:h-32 mb-3 sm:mb-4 opacity-60"
                        />
                        <span className="text-[#674941] text-xs sm:text-sm md:text-base lg:text-lg text-center px-4">
                          No users are currently live streaming
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ) : (
            // Streaming or Viewing UI
            <div className="relative  rounded-xl sm:rounded-2xl shadow-lg overflow-hidden flex flex-col font-Ubuntu ">
              <div className="flex gap-2 sm:gap-3 items-center justify-between px-4 border-b-[1px] border-gray-100">
                <div className="flex gap-4 items-center">
                  <img src={video_icon} className="h-11 w-11" />
                  <span className="h-16 w-px bg-gray-200"></span>
                  <p className="sm:text-[14px] md:text-[22px] text-[#373131] font-medium ">
                    Live Streaming
                  </p>
                </div>
                <div className="flex gap-2 items-center sm:items-baseline ">
                  <span className="flex items-center gap-2 bg-[#EB5757] text-[#FFFFFF] px-2 py-2 sm:px-3 sm:py-2 rounded-[8px] text-xs sm:text-sm font-semibold ">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3 sm:h-4 sm:w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    <span className="hidden sm:inline">{viewerCount}</span>
                    <span className="sm:hidden">{viewerCount}</span>
                  </span>
                  {isStreaming && !isViewingStream && (
                    <div
                      onClick={() => {
                        const streamLink = `${window.location.origin}/u/${roomId}`;
                        navigator.clipboard
                          .writeText(streamLink)
                          .then(() => {
                            setApiMessage(
                              "success",
                              "Universal stream link copied to clipboard!"
                            );
                          })
                          .catch(() => {
                            setApiMessage("error", "Failed to copy link");
                          });
                      }}
                      className="bg-Red text-white p-1 px-2 sm:p-2 rounded-[10px] flex items-center gap-1 cursor-pointer"
                      title="Copy universal stream link "
                    >
                      <img src={Link} className="h-[18px] w-[18px]" />
                      <span className="h-6 w-px bg-white"></span>
                      <p className="text-white text-[10px] lg:text-[12px]">
                        Share Link
                      </p>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex gap-4 flex-col lg:flex-row">
                <div className="flex-1 ">
                  {/* Video Area */}
                  <div className="relative aspect-video bg-gray-100  overflow-hidden flex items-center justify-center p-4 sm:p-10 ">
                    <video
                      ref={localVideoRef}
                      autoPlay
                      playsInline
                      muted={isStreaming && !isViewingStream}
                      className="w-full h-full rounded-[12px] object-cover   "
                      style={{ display: isCameraOff ? "none" : "block" }}
                    />
                    {/* Show placeholder if camera is off */}
                    {isCameraOff && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black text-white">
                        <span className="text-lg sm:text-xl font-bold">
                          Camera is Off
                        </span>
                      </div>
                    )}
                    {/* Show loading indicator for viewers waiting for stream */}
                    {isViewingStream && !remoteStream && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-Red text-center">
                          <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-Red mx-auto mb-3 sm:mb-4"></div>
                          <p className="text-sm sm:text-base">
                            Connecting to stream...
                          </p>
                        </div>
                      </div>
                    )}
                    {/* {isStreaming && !isViewingStream && (
                      <span className="absolute top-2 sm:top-4 left-2 sm:left-4 bg-gradient-to-r from-[#563D39] to-[#BC857D] text-white px-2 sm:px-4 py-1 rounded-full text-xs sm:text-base font-bold shadow-lg animate-pulse">
                        LIVE
                      </span>
                    )} */}
                    <span className="  text-xs sm:text-[17px] text-white font-medium  absolute bottom-0 sm:bottom-16 left-2 sm:left-16">
                      {isStreaming && !isViewingStream ? (
                        <>
                          <span className="font-bold bg-[#0000004D] rounded-full p-2 pb-3 px-7 flex items-center">
                            {username}
                          </span>
                        </>
                      ) : (
                        "Viewing stream"
                      )}
                    </span>
                  </div>
                  {/* End/Leave Button */}
                  <div className="w-full py-3 px-3 sm:px-4 lg:px-6 bg-white">
                    <div className="flex items-center justify-between w-full">
                      {/* Left empty space to balance center layout */}
                      <div className="w-[50px] sm:w-[100px]" />

                      {/* Center Mic & Camera buttons */}
                      {isStreaming && !isViewingStream && (
                        <div className="flex gap-2 sm:gap-3 justify-center items-center">
                          {/* Mic Button */}
                          <button
                            onClick={toggleMute}
                            className={`p-2 sm:p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110 w-[35px] h-[35px] sm:w-[47px] sm:h-[47px] flex items-center justify-center ${
                              isMuted
                                ? "bg-[#EB5757] text-white"
                                : "bg-Red text-white hover:bg-[#563D39]"
                            }`}
                            title={isMuted ? "Unmute" : "Mute"}
                          >
                            {isMuted ? (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"
                                  clipRule="evenodd"
                                />
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2"
                                />
                              </svg>
                            ) : (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                                />
                              </svg>
                            )}
                          </button>

                          {/* Camera Button */}
                          <button
                            onClick={toggleCamera}
                            className={`p-[13px] sm:p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-105 w-[35px] h-[35px] sm:w-[47px] sm:h-[47px] flex items-center justify-center ${
                              isCameraOff
                                ? "bg-[#EB5757] text-white"
                                : "bg-Red text-white hover:bg-[#563D39]"
                            }`}
                            title={
                              isCameraOff ? "Turn on camera" : "Turn off camera"
                            }
                          >
                            {isCameraOff ? (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M3 3l18 18M15 10l4.553-3.036A1 1 0 0121 7.764v8.472a1 1 0 01-1.447.9L15 14M9.879 9.88L8 8v8h6l1.121 1.121M9.879 9.88L8 8v8h6l1.121 1.121M3 3v12a2 2 0 002 2h9"
                                />
                              </svg>
                            ) : (
                              <img src={video} className="h-[40px] w-[40px]" />
                            )}
                          </button>
                        </div>
                      )}

                      {/* End Stream Button aligned to the end (right) */}
                      <div>
                        <div
                          onClick={endLiveStream}
                          className="px-4 py-2 sm:px-5 sm:py-2.5 bg-[#EB5757] text-white rounded-[7px] font-bold text-xs sm:text-sm cursor-pointer"
                        >
                          {isStreaming && !isViewingStream
                            ? "End Stream"
                            : "Leave Stream"}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  {/* Comments/Chat Panel */}
                  {(isStreaming || isViewingStream) && (
                    <aside className="w-full lg:w-[350px] xl:w-[400px] flex flex-col bg-white max-h-[500px] sm:max-h-[600px] lg:max-h-[600px] h-[400px] lg:h-[520px] xl:min-h-[625px]  2xl:min-h-[675px] relative order-1 lg:order-2 ">
                      <div className="p-3 sm:p-4  ">
                        <h3 className="font-bold text-[#563D39] text-base sm:text-lg">
                          Live Chat
                        </h3>
                      </div>
                      <div
                        className="flex-1 overflow-y-auto p-3 sm:p-4 space-y-3 sm:space-y-4 thin-scrollbar mb-16 lg:mb-0 lg:pb-20 relative bg-gray-50"
                        id="comments-list"
                        ref={commentsContainerRef}
                        onScroll={handleScroll}
                      >
                        {/* Scroll to bottom button */}
                        {showScrollToBottom && comments.length > 0 && (
                          <button
                            onClick={scrollToBottom}
                            className="absolute bottom-20 right-3 sm:right-4 z-10 bg-gradient-to-r from-[#563D39] to-[#BC857D] text-white p-2 rounded-full shadow-lg hover:scale-110 transition-all duration-200"
                            title="Scroll to bottom"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-3 w-3 sm:h-4 sm:w-4"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 14l-7 7m0 0l-7-7m7 7V3"
                              />
                            </svg>
                          </button>
                        )}
                        {comments.length === 0 ? (
                          <div className="flex flex-col items-center justify-center h-full text-[#674941] opacity-60">
                            <span className="text-sm sm:text-base">
                              No comments yet
                            </span>
                          </div>
                        ) : (
                          comments.map((comment, index) => (
                            <div
                              key={index}
                              className={`flex items-end gap-2 ${
                                comment.isOwnMessage
                                  ? "justify-end"
                                  : "justify-start"
                              }`}
                            >
                              {/* Avatar/Initials */}
                              {!comment.isOwnMessage && (
                                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gradient-to-r from-[#563D39] to-[#BC857D] flex items-center justify-center text-white font-bold text-xs sm:text-sm flex-shrink-0">
                                  {comment.username?.[0]?.toUpperCase() || "?"}
                                </div>
                              )}
                              <div
                                className={`max-w-[75%] px-3 sm:px-4 py-2 rounded-2xl shadow ${
                                  comment.isOwnMessage
                                    ? "bg-gradient-to-r from-[#F9F9F9] to-[#E0E0E0] text-[#563D39] ml-auto"
                                    : "bg-[#F9F9F9] text-[#674941]"
                                }`}
                              >
                                <div className="flex items-center gap-2 mb-1">
                                  <span className=" text-[9px] text-gray-500">
                                    {comment.username}
                                  </span>
                                  {/* <span className="text-xs text-[#BC857D]">
                          {comment.timestamp instanceof Date
                            ? comment.timestamp.toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                              })
                            : new Date(comment.timestamp).toLocaleTimeString(
                                [],
                                {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                }
                              )}
                        </span> */}
                                </div>
                                <p className="text-xs sm:text-[13px] text-black break-words">
                                  {comment.message}
                                </p>
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                      {/* Sticky Input */}
                      <div className="absolute bottom-0 left-0 w-full p-2 sm:p-3 bg-white ">
                        <div className="flex items-center gap-2 relative">
                          {/* Input with Send Icon Inside */}
                          <div className="relative flex-1 ">
                            <input
                              type="text"
                              placeholder="Add a comment..."
                              className=" px-4 sm:px-5 py-2.5 w-full pl-10 pr-12 sm:pl-10 sm:pr-10 rounded-[10px] font-Ubuntu placeholder-white text-xs sm:text-sm focus:border-Red focus:ring-0 focus:outline-none bg-Red text-white"
                              value={commentInput}
                              onChange={(e) => setCommentInput(e.target.value)}
                              onKeyPress={(e) =>
                                e.key === "Enter" &&
                                e.target.value.trim() &&
                                sendComment()
                              }
                            />
                            {/* Emoji Picker Icon */}
                            <div className="">
                              <button
                                onClick={() =>
                                  setShowEmojiPicker(!showEmojiPicker)
                                }
                                className="text-white text-xl  p-2 rounded-full absolute left-0 top-1/2 -translate-y-1/2"
                              >
                                <MdOutlineEmojiEmotions />
                              </button>

                              {showEmojiPicker && (
                                <div
                                  ref={emojiPickerRef}
                                  className="absolute bottom-full mb-2 z-50"
                                >
                                  <EmojiPicker
                                    onEmojiClick={(emojiData) =>
                                      setCommentInput(
                                        (prev) => prev + emojiData.emoji
                                      )
                                    }
                                    theme="white"
                                  />
                                </div>
                              )}
                            </div>
                            <div
                              onClick={sendComment}
                              disabled={!commentInput.trim()}
                              className={`absolute right-1.5 top-1/2 -translate-y-1/2  bg-white p-2 sm:p-2 md:p-2 lg:p-2 rounded-[5px] ${
                                commentInput.trim()
                                  ? "text-white hover:scale-110"
                                  : "text-gray-300 cursor-not-allowed"
                              }`}
                            >
                              <img
                                src={Send}
                                alt=""
                                className=" h-3 w-3 sm:h-4 sm:w-4 sm:p-px "
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </aside>
                  )}
                </div>
              </div>
            </div>
          )}
        </section>
      </main>

      <style jsx>{`
        .thin-scrollbar::-webkit-scrollbar {
          width: 4px;
        }
        .thin-scrollbar::-webkit-scrollbar-thumb {
          background: #e0e0e0;
          border-radius: 4px;
        }
        .thin-scrollbar::-webkit-scrollbar-track {
          background: #f9f9f9;
        }

        .emoji-scrollbar::-webkit-scrollbar {
          height: 3px;
        }
        .emoji-scrollbar::-webkit-scrollbar-thumb {
          background: #bc857d;
          border-radius: 2px;
        }
        .emoji-scrollbar::-webkit-scrollbar-track {
          background: #f0f0f0;
          border-radius: 2px;
        }

        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }

        /* Mobile-specific styles */
        @media (max-width: 640px) {
          .emoji-scrollbar::-webkit-scrollbar {
            height: 2px;
          }

          /* Improve touch targets on mobile */
          button {
            min-height: 44px;
            min-width: 44px;
          }
        }

        /* Tablet optimizations */
        @media (min-width: 641px) and (max-width: 1024px) {
          .emoji-scrollbar::-webkit-scrollbar {
            height: 4px;
          }
        }

        /* Desktop enhancements */
        @media (min-width: 1025px) {
          .emoji-scrollbar::-webkit-scrollbar {
            height: 6px;
          }
        }
      `}</style>

      {/* WebRTC Viewer Generator Modal */}
      {showWebRTCViewer && (
        <WebRTCViewerGenerator
          roomId={roomId}
          onClose={() => setShowWebRTCViewer(false)}
        />
      )}
    </div>
  );
};

export default LiveStreamApp;
