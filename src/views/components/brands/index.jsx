import React, {
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
} from "react";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import Spinner from "../../../helpers/UI/Spinner";
import { IntlContext } from "../../../App";
import Facebook from "../../../assets/images/Analytics/facebook.svg";
import PinterestLogo from "../../../assets/images/Analytics/pinterest.svg";
import VimoLogo from "../../../assets/images/Analytics/vimeo.svg";
import LinkedInLogo from "../../../assets/images/Analytics/linkedIn.svg";
import TwitterLogo from "../../../assets/images/Analytics/X.svg";
import TiktokLogo from "../../../assets/images/Analytics/tiktok.svg";
import RedditLogo from "../../../assets/images/Analytics/reddit.svg";
import ThreadsLogo from "../../../assets/images/Analytics/thread.svg";
import InstagramLogo from "../../../assets/images/Analytics/instagram.svg";
import YoutubeLogo from "../../../assets/images/Analytics/youtube.svg";
import TumblrLogo from "../../../assets/images/Analytics/tumblr.svg";
import Mastodon from "../../../assets/images/Analytics/mastodon.svg";
import addBrand from "../../../assets/images/svg_icon/addBrand.svg";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";
import dummyProfile from "../../../assets/images/dummytprofile.svg";
import { Dialog } from "@mui/material";
import Cancel from "../../../assets/images/Analytics/Cance_icon.svg";
import ConnectDialog from "./ConnectPopup";
import TelegramMobile from "./Telegram_mobile";
import { setApiMessage } from "../../../helpers/context/toaster";
import { useBrand } from "../../../helpers/context/BrandContext";
import Telegram from "../../../assets/images/Analytics/telegram.svg";

const PlatformManagement = () => {
  const { selectedBrand } = useBrand();
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;

  const platformConfig = {
    facebook: { color: "#0866FF", icon: Facebook },
    pinterest: { color: "#E60023", icon: PinterestLogo },
    vimeo: { color: "#1EB8EB", icon: VimoLogo },
    linkedin: { color: "#0A66C2", icon: LinkedInLogo },
    twitter: { color: "#100E0F", icon: TwitterLogo },
    tiktok: { color: "#000000", icon: TiktokLogo },
    reddit: { color: "#FC471E", icon: RedditLogo },
    thread: { color: "#000000", icon: ThreadsLogo },
    instagram: { color: "#C30FB2", icon: InstagramLogo },
    youtube: { color: "#FF0302", icon: YoutubeLogo },
    tumblr: { color: "#35465C", icon: TumblrLogo },
    x: { color: "#000000", icon: TwitterLogo },
    telegram: { color: "#0088cc", icon: Telegram },
    mastodon: { color: "#6364FF", icon: Mastodon },
  };

  // Centralized social links cache
  const [socialLinksCache, setSocialLinksCache] = useState(new Map());
  const [isLoadingSocialLinks, setIsLoadingSocialLinks] = useState(false);

  const fetchSocialProfile = useCallback(
    async (brandId) => {
      if (socialLinksCache.has(brandId)) {
        return socialLinksCache.get(brandId);
      }

      try {
        const response = await apiInstance.get(URL.SHARE_PROFILE, {
          headers: {
            Authorization: `Bearer ${token}`,
            brand: brandId,
          },
        });

        if (response.data?.status && response.data?.profile) {
          const profileData = {
            social_links: response.data.profile.social_links || [],
            profile: response.data.profile,
          };

          // Cache the result
          setSocialLinksCache(
            (prev) => new Map(prev.set(brandId, profileData))
          );
          return profileData;
        }
        return { social_links: [], profile: null };
      } catch (error) {
        console.error("Error fetching social profile:", error);
        return { social_links: [], profile: null };
      }
    },
    [token, socialLinksCache]
  );

  // Get current brand's social data from cache or fetch if needed
  const getCurrentBrandSocialData = useCallback(
    async (brandId) => {
      if (!brandId) return { social_links: [], profile: null };

      const cached = socialLinksCache.get(brandId);
      if (cached) {
        return cached;
      }

      // Fetch if not cached
      const profileData = await fetchSocialProfile(brandId);
      return profileData;
    },
    [socialLinksCache, fetchSocialProfile]
  );

  // Update current brand social data when selectedBrand changes
  useEffect(() => {
    if (selectedBrand?.id) {
      getCurrentBrandSocialData(selectedBrand.id);
    }
  }, [selectedBrand?.id, getCurrentBrandSocialData]);

  // Platform View Component
  const PlatformsView = () => {
    const [dialogOpen, setDialogOpen] = useState(false);
    const [selectedPlatform, setSelectedPlatform] = useState(null);
    const [localLoading, setLocalLoading] = useState(false);
    // Disconnect modal state
    const [disconnectModalOpen, setDisconnectModalOpen] = useState(false);
    const [platformToDisconnect, setPlatformToDisconnect] = useState(null);
    const [disconnecting, setDisconnecting] = useState(false);
    // Telegram mobile dialog state
    const [telegramMobileOpen, setTelegramMobileOpen] = useState(false);

    // Get social links from cache instead of making API call
    const socialLinksData = useMemo(() => {
      if (!selectedBrand?.id) return { social_links: [], profile: null };
      return (
        socialLinksCache.get(selectedBrand.id) || {
          social_links: [],
          profile: null,
        }
      );
    }, [selectedBrand?.id, socialLinksCache]);

    const handlePlatformClick = (platform, isActive) => {
      if (!isActive) {
        setSelectedPlatform(platform);
        setDialogOpen(true);
      }
    };

    const handleConnectConfirm = async () => {
      try {
        // Check if platform is Telegram
        if (selectedPlatform?.toLowerCase() === "telegram") {
          setDialogOpen(false);
          setTelegramMobileOpen(true);
          return;
        }

        setLocalLoading(true);
        const BrandId = selectedBrand.id;
        const platformName = selectedPlatform?.toLowerCase().trim();
        const apiUrl = URL[`${selectedPlatform.toUpperCase()}`];
        let response;
        if (platformName === "mastodon") {
          // Use POST for Mastodon connect
          const formData = new FormData();
          formData.append("instance_url", "https://mastodon.social");
          response = await apiInstance.post(apiUrl, formData, {
            headers: { brand: BrandId },
          });
        } else {
          // Use GET for all other platforms
          response = await apiInstance.get(apiUrl, {
            headers: { brand: BrandId },
          });
        }
        if (response.data?.status) {
          if (response.data.url) {
            window.location.href = response.data.url;
          } else {
            setDialogOpen(false);
            // Invalidate cache and refetch for this brand
            setSocialLinksCache((prev) => {
              const newCache = new Map(prev);
              newCache.delete(BrandId);
              return newCache;
            });
            await fetchSocialProfile(BrandId);
          }
        } else {
          setApiMessage("error", response.data.message);
        }
      } catch (error) {
        console.error("Error connecting platform:", error);
      } finally {
        setLocalLoading(false);
      }
    };

    // Disconnect logic
    const handleAvatarClick = (platform) => {
      setPlatformToDisconnect(platform);
      setDisconnectModalOpen(true);
    };

    const handleDisconnect = async () => {
      if (!platformToDisconnect) return;
      setDisconnecting(true);
      try {
        const BrandId = selectedBrand.id;
        // Normalize platform name for URL key
        let normalized = platformToDisconnect.platform?.toLowerCase();
        if (normalized === "threads") normalized = "thread";
        const apiUrl = URL[`DISCONNECT_${normalized.toUpperCase()}`];

        // Special condition for Telegram - use POST method
        if (normalized === "telegram") {
          await apiInstance.post(apiUrl, {}, { headers: { brand: BrandId } });
        } else {
          // For all other platforms, use GET method
          await apiInstance.get(apiUrl, { headers: { brand: BrandId } });
        }

        // Invalidate cache and refetch for this brand
        setSocialLinksCache((prev) => {
          const newCache = new Map(prev);
          newCache.delete(BrandId);
          return newCache;
        });
        await fetchSocialProfile(BrandId);
        setDisconnectModalOpen(false);
        setPlatformToDisconnected(null);
      } catch (error) {
        setApiMessage("error", "Failed to disconnect platform");
      } finally {
        setDisconnecting(false);
      }
    };

    return (
      <>
        <div className="max-w-[1920px] flex flex-col justify-start items-center mx-auto bg-white pb-6 min-h-screen mt-8 rounded-[8px] px-4 sm:px-6 md:px-8">
          {isLoadingSocialLinks || localLoading ? (
            <div className="flex justify-center items-center w-full h-[75vh]">
              <Spinner />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-6 w-[80%] mt-10">
              {socialLinksData.social_links.length > 0 ? (
                socialLinksData.social_links.map((link) => {
                  const config = platformConfig[link.platform?.toLowerCase()];
                  const isActive = link.user_status;
                  const isPlatformStatus = link.platform_status;

                  if (isPlatformStatus === "2" || !config) return null;

                  return (
                    <div
                      key={link.platform}
                      className="relative flex items-center h-[69px] w-full bg-white border border-[#E0E0E0] rounded-[12px] "
                    >
                      <div
                        className="flex items-center justify-center h-full w-[45px] relative rounded-tl-[12px] rounded-bl-[12px] "
                        style={{ backgroundColor: config.color }}
                      >
                        <div className="bg-white p-2 rounded-full absolute top-[12px] -right-[20px] shadow-lg">
                          <img
                            src={config.icon}
                            alt={link.platform}
                            className="w-7 h-7"
                          />
                        </div>
                      </div>

                      <div className="flex-1 ml-4 sm:ml-6 text-sm font-medium text-gray-800 truncate">
                        {isActive
                          ? link.username || "Unnamed"
                          : "Not Connected"}
                      </div>

                      <div className="mr-4 shrink-0 w-8 h-8 r">
                        {isActive ? (
                          <div
                            className="relative cursor-pointer"
                            onClick={() => handleAvatarClick(link)}
                          >
                            <div className=" w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">
                              {(() => {
                                const isDummy = !link?.profile_image;

                                return (
                                  <img
                                    src={link?.profile_image || dummyProfile}
                                    alt="Brand"
                                    className={`object-cover rounded-full ${
                                      isDummy
                                        ? "w-[18px] h-[18px]"
                                        : "w-[32px] h-[32px]"
                                    }`}
                                  />
                                );
                              })()}
                            </div>
                          </div>
                        ) : (
                          <button
                            onClick={() =>
                              handlePlatformClick(link.platform, isActive)
                            }
                            className="w-full h-full flex items-center justify-center "
                          >
                            <img
                              src={addBrand}
                              alt="+"
                              className="w-full h-full object-contain"
                            />
                          </button>
                        )}
                      </div>
                      {isActive && (
                        <div
                          className="h-4 w-6 absolute -top-[5px] -right-[12px] z-40 cursor-pointer"
                          onClick={() => handleAvatarClick(link)}
                        >
                          <img
                            src={Cancel}
                            alt=""
                            className=" rounded-full bg-Red h-4 w-4 p-1  "
                          />
                        </div>
                      )}
                    </div>
                  );
                })
              ) : (
                <div className="col-span-2 text-center py-10 text-gray-500">
                  No platforms available
                </div>
              )}
            </div>
          )}
        </div>

        {/* Connect Dialog */}
        <ConnectDialog
          open={dialogOpen}
          onClose={() => setDialogOpen(false)}
          platform={selectedPlatform}
          onConfirm={handleConnectConfirm}
        />

        {/* Telegram Mobile Dialog */}
        <Dialog
          open={telegramMobileOpen}
          onClose={() => setTelegramMobileOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <TelegramMobile
            onClose={() => setTelegramMobileOpen(false)}
            onSuccess={async () => {
              // Invalidate cache and refetch for this brand
              setSocialLinksCache((prev) => {
                const newCache = new Map(prev);
                newCache.delete(selectedBrand.id);
                return newCache;
              });
              await fetchSocialProfile(selectedBrand.id);
            }}
          />
        </Dialog>

        {/* Disconnect Confirmation Modal */}
        <Dialog
          open={disconnectModalOpen}
          onClose={() => setDisconnectModalOpen(false)}
        >
          <div
            className="fixed inset-0 bg-black/50 flex items-center justify-center px-4 font-Ubuntu "
            onClick={() => setDisconnectModalOpen(false)}
          >
            <div
              className="bg-white rounded-2xl shadow-xl w-full max-w-sm p-6 relative"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
                onClick={() => setDisconnectModalOpen(false)}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M18 6L6 18M6 6L18 18"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>

              {/* Modal Content */}
              <div className="flex flex-col items-start justify-center text-center">
                {/* Platform Icon */}
                <div className="w-[45px] h-[45px] rounded-full flex items-center justify-center mb-4">
                  {platformToDisconnect &&
                    platformConfig[platformToDisconnect.platform?.toLowerCase()]
                      ?.icon && (
                      <img
                        src={
                          platformConfig[
                            platformToDisconnect.platform?.toLowerCase()
                          ].icon
                        }
                        alt={platformToDisconnect.platform}
                        className="w-[45px] h-[45px]"
                      />
                    )}
                </div>

                {/* Title */}
                <h2 className="text-[18px] font-bold text-[#000000] mb-1 capitalize">
                  Disconnect {platformToDisconnect?.platform}
                </h2>

                {/* Platform Name */}
                <div
                  className="text-[18px] font-bold mb-1 capitalize mt-[15px]"
                  style={{
                    color:
                      platformToDisconnect &&
                      platformConfig[
                        platformToDisconnect.platform?.toLowerCase()
                      ]?.color,
                  }}
                >
                  {platformToDisconnect?.platform}
                </div>

                {/* Message */}
                <p className="text-[#46484a] text-[14px] font-normal leading-[20px] max-w-xs text-left">
                  Are you sure you want to disconnect this platform from your
                  account?
                </p>
              </div>

              {/* Buttons */}
              <div className="flex justify-end gap-2 mt-6">
                <button
                  onClick={() => setDisconnectModalOpen(false)}
                  className="px-5 py-2 text-sm border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-100"
                  disabled={disconnecting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleDisconnect}
                  className="px-5 py-2 text-sm bg-[#4B2C27] text-white rounded-lg hover:bg-[#3c221f]"
                  disabled={disconnecting}
                >
                  {disconnecting ? "Disconnecting..." : "Disconnect"}
                </button>
              </div>
            </div>
          </div>
        </Dialog>
      </>
    );
  };

  return (
    <div className="mx-[30px] p-6 bg-white rounded-lg shadow-lg min-h-screen overflow-y-auto font-Ubuntu mt-[20px]">
      <PlatformsView />
    </div>
  );
};

export default PlatformManagement;
