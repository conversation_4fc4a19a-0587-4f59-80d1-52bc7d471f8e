import React, { Suspense, useContext, useEffect, useState } from "react";
import profileImage from "../../../assets/images/svg_icon/user-icon.svg";
import { URL } from "../../../helpers/constant/Url";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import SocialMediaIcons from "./SocialMediaIcons";
import { setApiMessage } from "../../../helpers/context/toaster";
import { IntlContext } from "../../../App";
import { renderHighlightedText } from "../../../helpers/context/common";
import Pagination from "../../admin/common/paginationCommon";
import Loader from "../../../helpers/UI/Loader";
import siteConstant from "../../../helpers/constant/siteConstant";
import { RxDashboard } from "react-icons/rx";
import { FaShareSquare } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { fetchProfile } from "../../../redux/slices/profileFetch";
import LoadingSpinner from "../../../helpers/UI/LoadingSpinner";
import Spinner from "../../../helpers/UI/Spinner";
import {
  useAccountSwitchReduxRefresh,
  useAccountSwitchApiRefresh,
} from "../../../helpers/hooks/useAccountSwitchRefresh.js";
import Banner from "../../../assets/images/svg_icon/Banner.jpg";
const EditProfileModel = React.lazy(() => import("./EditProfile"));
const GridPost = React.lazy(() => import("./GridPost"));
const PostList = React.lazy(() => import("./PostList"));

const ProfilePage = () => {
  const rProfile = useSelector((state) => state?.profile?.data);
  const rPosts = useSelector((state) => state?.profile?.posts);
  const rLoading = useSelector((state) => state?.profile?.isLoading);
  const rPages = useSelector((state) => state?.profile?.totalPages);

  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editProfileOpen, setEditProfileOpen] = useState(false);
  const [posts, setPosts] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [postgrid, setPostgrid] = useState(true);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const navigate = useNavigate();
  const [istable, setIstable] = useState(postgrid);
  const [isPageLoading, setIsPageLoading] = useState(false);

  const dispatch = useDispatch();

  useEffect(() => {
    setProfile(rProfile);
    setPosts(rPosts);
    setTotalPages(rPages);
  }, [rProfile, rPosts, rPages]);

  useEffect(() => {
    setLoading(rLoading);
  }, [rLoading]);

  useEffect(() => {
    dispatch(fetchProfile());
  }, []);

  // Refresh profile data when account is switched
  useAccountSwitchReduxRefresh(dispatch, [fetchProfile], [dispatch]);

  const fetchPosts = async (page) => {
    try {
      setIsPageLoading(true);
      const { status, data } = await apiInstance.get(
        `${URL.USER_POSTS}?page=${page}&request_platform=2`
      );
      if (data?.results?.status) {
        setProfile({ ...profile, total_post_count: data?.count });
        setPosts(data?.results?.data);
        setTotalPages(Math.ceil(data?.count / 10));
      } else {
        setApiMessage("error", data?.results?.message || data?.message);
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setIsPageLoading(false);
    }
  };

  useEffect(() => {
    if (currentPage > 1) {
      fetchPosts(currentPage);
    } else {
      setPosts(profile?.posts);
    }
  }, [currentPage]);

  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePageClick = (page) => {
    setCurrentPage(page);
  };

  const handleGridPost = () => {
    setPostgrid(!postgrid);
    setIstable(!istable);
  };

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="bg-[#FFFFFF] min-w-full pb-20 sm:pb-20  mt-[26px]">
            <div className="">
              <div className="relative">
                <img
                  src={Banner || Banner}
                  alt="Banner"
                  className="w-full h-[280px] object-cover"
                />
                <button
                  onClick={() => setEditProfileOpen(true)}
                  className="absolute top-4 right-4 text-[#FFFFFF] border border-[#FFFFFF] h-[40px] w-[110px] px-4 py-1 rounded-[10px] text-[20px]  font-medium"
                >
                  Edit
                </button>
              </div>
              {/* Top Profile Section */}
              <div className="bg-white rounded-lg p-6  border-b border-[#E0E0E0]">
                <div className="flex flex-col justify-between items-center mb-6">
                  <div className="flex flex-col items-center gap-4 text-center -mt-16 z-10">
                    <img
                      src={profile?.profile_image || siteConstant.SOCIAL_ICONS.DUMMY_PROFILE}
                      alt="Avatar"
                      className={`${
                        profile?.profile_image
                          ? "w-20 h-20 rounded-full object-cover"
                          : "h-20 w-20 rounded-full border p-2"
                      }`}
                    />
                    <div>
                      <h1 className="text-[24px] font-normal text-[#000000]">
                        {profile?.name}
                      </h1>
                      <p className="text-[#000000] font-normal text-base mt-1">
                        {profile?.bio || ""}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Username and Joined Date */}
                <div className="flex justify-center items-center gap-2 text-center mb-4 -mt-3">
                  <span className="text-[#000000] text-base">
                    @{profile?.username}
                  </span>
                </div>

                {/* Stats Section */}
                <div className="flex justify-center items-center">
                  <div className="flex flex-col items-center px-4">
                    <span className="font-bold text-gray-800">
                      {profile?.total_post_count || 0}
                    </span>
                    <span className="text-[#000000] text-base">Post</span>
                  </div>
                  <div className="h-8 border-l border-[#000000] mx-4"></div>

                  <div className="flex flex-col items-center px-4">
                    <span className="font-bold text-gray-800">
                      {profile?.number_of_followers || 0}
                    </span>
                    <span className="text-[#000000] text-base">Follower</span>
                  </div>

                  {/* Divider */}
                  <div className="h-8 border-l border-[#000000] mx-4"></div>

                  <div className="flex flex-col items-center px-4">
                    <span className="font-bold text-gray-800">
                      {profile?.number_of_following || 0}
                    </span>
                    <span className="text-[#000000] text-base">Following</span>
                  </div>

                  {/* Divider */}
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-[1.5fr_1fr] custom-width:grid-cols-[1.5fr_1fr_1fr] gap-5">
                <div className=" col-span-full sm:col-span-0">
                  {/* <div className="flex justify-end items-center mb-5">
                    <div className="flex  gap-4 pe-1">
                      <div>
                        <div
                          onClick={handleGridPost}
                          className={
                            postgrid
                              ? "bg-Red text-white p-2 rounded-lg font-bold"
                              : " p-2 rounded-lg bg-profileCardBG font-bold text-Red"
                          }
                        >
                          <RxDashboard className="h-6 w-6 " />
                        </div>
                      </div>
                      <div>
                        <button
                          onClick={handleGridPost}
                          className={
                            !postgrid
                              ? "bg-Red p-2 rounded-lg"
                              : " p-2 rounded-lg bg-profileCardBG"
                          }
                        >
                          <img
                            src={siteConstant.SOCIAL_ICONS.POST_LIST_ICON}
                            className={
                              !postgrid
                                ? "filter invert brightness-0"
                                : " h-6 w-6"
                            }
                          />
                        </button>
                      </div>
                    </div>
                  </div> */}
                  {loading || isPageLoading ? (
                    <div className="flex justify-center items-center mt-10 mb-10">
                      <Spinner />
                    </div>
                  ) : postgrid ? (
                    <div className="">
                      <GridPost
                        userData={profile}
                        fetchPosts={fetchPosts}
                        istable={istable}
                        posts={posts}
                        fetchProfile={() => dispatch(fetchProfile())}
                      />
                    </div>
                  ) : (
                    <PostList
                      // singleData={singleData}
                      // setSingleData={setSingleData}
                      userData={profile}
                      fetchPosts={fetchPosts}
                      posts={posts}
                    />
                  )}
                </div>
              </div>
              <div className="flex justify-center items-center">
                <Pagination
                  totalPages={totalPages}
                  currentPage={currentPage}
                  handlePrevious={handlePrevious}
                  handleNext={handleNext}
                  handlePageClick={handlePageClick}
                  isLoading={isPageLoading}
                />
              </div>
            </div>
          </div>

          <Suspense fallback="">
            {editProfileOpen && (
              <EditProfileModel
                profile={profile}
                open={editProfileOpen}
                handleDialogClose={() => setEditProfileOpen(!editProfileOpen)}
                fetchProfile={() => dispatch(fetchProfile())}
              />
            )}
          </Suspense>
        </>
      )}
    </>
  );
};

export default ProfilePage;
