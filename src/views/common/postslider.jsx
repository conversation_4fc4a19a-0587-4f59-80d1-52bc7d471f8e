import React, { useContext, useState, useRef, useEffect } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import siteConstant from "../../helpers/constant/siteConstant";
import { FaChevronRight } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import { IntlContext } from "../../App";
import { getFileType } from "../../helpers/constant/utils";
import { renderHighlightedText } from "../../helpers/context/common";
import SocialMediaIcons from "../components/profile/SocialMediaIcons";

const CustomNextArrow = ({ onClick }) => (
  <div
    className="absolute bottom-[-20px] right-[-10px] transform -translate-y-1/2 z-10 cursor-pointer bg-profileCardBG p-2 rounded-full"
    onClick={onClick}
  >
    <FaChevronRight className="text-lightyellow hover:text-Red" />
  </div>
);

const CustomPrevArrow = ({ onClick }) => (
  <div
    className="absolute bottom-[-20px] left-[-10px] transform -translate-y-1/2 z-10 cursor-pointer bg-profileCardBG p-2 rounded-full"
    onClick={onClick}
  >
    <FaChevronRight className="rotate-180 text-lightyellow hover:text-Red" />
  </div>
);

// Profile Image Component with error handling
const ProfileImage = ({ profileImage, userName }) => {
  const [imageError, setImageError] = useState(false);
  const [currentImageSrc, setCurrentImageSrc] = useState(null);

  // Reset error state when profileImage changes
  useEffect(() => {
    if (profileImage !== currentImageSrc) {
      setImageError(false);
      setCurrentImageSrc(profileImage);
    }
  }, [profileImage, currentImageSrc]);

  const handleImageError = (e) => {
    e.target.onerror = null;
    setImageError(true);
    e.target.src = siteConstant.SOCIAL_ICONS.DUMMY_PROFILE;
  };

  const getImageClassName = () => {
    if (imageError) {
      return "rounded-full h-11 w-11 sm:h-14 sm:w-14 lg:h-15 lg:w-15 border p-1 sm:p-2";
    }
    return "p-[5px] rounded-full h-11 w-11 sm:h-14 sm:w-14 lg:h-15 lg:w-15 object-cover";
  };

  return (
    <img
      src={profileImage || siteConstant.SOCIAL_ICONS.DUMMY_PROFILE}
      alt={`${userName || "User"} Profile`}
      className={getImageClassName()}
      onError={handleImageError}
    />
  );
};

const Postslider = ({ posts, isMostValuable, sectionTitle }) => {
  const [isExpanded, setIsExpanded] = useState({});
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const sliderRef = useRef(null);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const navigate = useNavigate();

  const getPlatformsFromPost = (post) => {
    if (!post) return {};

    return {
      facebook: post.facebook || false,
      instagram: post.instagram || false,
      linkedin: post.linkedin || false,
      pinterest: post.pinterest || false,
      vimeo: post.vimeo || false,
      youtube: post.youtube || false,
      dailymotion: post.dailymotion || false,
      reddit: post.reddit || false,
      tumblr: post.tumblr || false,
      twitter: post.twitter || false,
      thread: post.thread || false,
      tiktok: post.tiktok || false,
    };
  };

  const hanldeView = () => {
    navigate("/profile");
  };

  // Video event handlers
  const handleVideoPlay = () => {
    setIsVideoPlaying(true);
    if (sliderRef.current) {
      sliderRef.current.slickPause();
    }
  };

  const handleVideoPause = () => {
    setIsVideoPlaying(false);
    if (sliderRef.current && posts.length > 1) {
      sliderRef.current.slickPlay();
    }
  };

  const handleVideoEnded = () => {
    setIsVideoPlaying(false);
    if (sliderRef.current && posts.length > 1) {
      sliderRef.current.slickPlay();
    }
  };

  const sortedPosts = posts.sort(
    (a, b) => new Date(b.created_at) - new Date(a.created_at)
  );
  const displayedPosts = sortedPosts.slice(0, 5);

  const outerSliderSettings = {
    dots: false,
    infinite: posts.length > 1,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    autoplay: posts.length > 1 && !isVideoPlaying,
    autoplaySpeed: 2200,
    swipe: false,
    draggable: false,
    touchMove: false,
  };

  const innerSliderSettings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    appendDots: (dots) => (
      <div style={{ top: "-40px", position: "relative" }}>
        <ul style={{ margin: "0px", color: "Red" }}>{dots}</ul>
      </div>
    ),
  };

  const toggleDescription = (index) => {
    setIsExpanded((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const truncatedDescription = (description) =>
    description.length > 100
      ? `${description.substring(0, 100)}...`
      : description;

  return (
    <div className="w-full mx-auto font-Ubuntu relative  ">
      <div className=" p-3 flex flex-col  text-gray-700 h-[522px] overflow-y-auto pb-12 ">
        <div className="flex justify-between items-center">
          <h2 className="text-[#563D39] font-medium text-[15px] md:text-[18px] ">
            {sectionTitle}
          </h2>
        </div>
        <Slider
          {...outerSliderSettings}
          ref={sliderRef}
          className="w-full h-[300px] pt-1"
        >
          {displayedPosts.map((post, index) => (
            <div key={index}>
              <div className=" flex gap-4 items-center">
                <div>
                  <ProfileImage
                    profileImage={post?.user?.profile_image}
                    userName={post?.user?.name}
                  />
                </div>
                <div className=" recent-text overflow-hidden">
                  <h5 className="text-[#000000] font-bold text-[15px]">
                    {post?.user?.name || "Unknown User"}
                  </h5>
                  <div className="flex flex-wrap gap-1 items-center">
                    <p className="text-[#A9ABAD] font-normal text-[14px]">
                      @{post?.user?.username || "unknown_username"}
                    </p>
                    <span className="text-[#A9ABAD]">·</span>

                    <p className="text-[#A9ABAD] font-normal text-[14px] ">
                      {new Date(post.created_at).toLocaleDateString("en-US", {
                        day: "2-digit",
                        month: "short",
                        year: "numeric",
                      })}
                    </p>
                  </div>
                </div>
              </div>

              <div className="pb-1">
                <div className="text-[#563D39] text-[16px] font-normal leading-snug line-clamp-2 overflow-hidden mt-1 mb-4">
                  <span className="font-bold mr-1 truncate max-w-[800px] block">
                    {post.title && post.title !== "''" ? post.title : null}
                  </span>
                  <span className="text-[#A9ABAD] text-[13px] font-normal align-baseline">
                    {renderHighlightedText(post.description)}
                  </span>
                </div>

                {post.files.length > 1 ? (
                  <Slider
                    {...innerSliderSettings}
                    className="w-full  rounded-[12px]"
                  >
                    {post.files.slice(0, 10).map((file, idx) => (
                      <div key={idx}>
                        {getFileType(file) === "video" ? (
                          <video
                            src={file}
                            controls
                            className="w-full h-[330px] object-cover rounded-[12px] "
                            onPlay={handleVideoPlay}
                            onPause={handleVideoPause}
                            onEnded={handleVideoEnded}
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src =
                                siteConstant.SOCIAL_ICONS.DUMMY_POST;
                            }}
                          />
                        ) : (
                          <img
                            src={file}
                            alt={`Post image ${idx + 1}`}
                            className="w-full h-[330px] object-contain rounded-[12px]"
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src =
                                siteConstant.SOCIAL_ICONS.DUMMY_POST;
                            }}
                          />
                        )}
                      </div>
                    ))}
                  </Slider>
                ) : (
                  <div>
                    {getFileType(post.files[0]) === "video" ? (
                      <video
                        src={post.files[0]}
                        controls
                        className="w-full h-[330px] object-cover rounded-[12px] "
                        onPlay={handleVideoPlay}
                        onPause={handleVideoPause}
                        onEnded={handleVideoEnded}
                      />
                    ) : (
                      <img
                        src={
                          post.files[0] || siteConstant.SOCIAL_ICONS.DUMMY_POST
                        }
                        alt="Post image"
                        className="w-full h-[330px] object-cover rounded-[12px] "
                      />
                    )}
                  </div>
                )}

                <div>
                  <div className="flex items-center gap-2 sm:gap-1 md:gap-4 whitespace-nowrap ">
                    <div className="flex-1 overflow-x-auto pe-2 thin-scrollbar mt-3">
                      <SocialMediaIcons
                        platforms={getPlatformsFromPost(post)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>
      {/* {!isMostValuable && (
        <div className="absolute bottom-0 left-0 right-0 text-center py-2 bg-white">
          <button
            className="bg-Red text-white font-bold py-2 px-8 rounded-[12px]  transition-colors"
            onClick={hanldeView}
          >
            View All
          </button>
        </div>
      )} */}
    </div>
  );
};

export default Postslider;
